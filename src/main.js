import { createApp } from 'vue';
import App from './App.vue';
import registerGlobalComponents from './plugins/globalComponents';
import { createPinia } from 'pinia';
import i18n, { loadLocaleMessages } from '@/lib/i18n'
import router from './router';

window.addEventListener('load', async function () {
    try {
        const isLocal = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';

        // Now that the translations are loaded, mount the Vue app
        const app = createApp(App);
        await loadLocaleMessages(i18n.global.locale.value);
        registerGlobalComponents(app);
        app.use(i18n).use(createPinia()).use(router);
        app.mount('#app');


    } catch (error) {
        console.error("Error initializing application:", error);
    }
});
