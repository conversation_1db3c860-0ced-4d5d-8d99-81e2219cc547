import { createI18n } from 'vue-i18n';

export const savedLanguage = localStorage.getItem('app_language') || 'en';

const i18n = createI18n({
  legacy: false,
  locale: savedLanguage,
  fallbackLocale: 'en',
  messages: {}, // start empty
  globalInjection: true,
});

export async function loadLocaleMessages(locale) {
  if (!i18n.global.availableLocales.includes(locale)) {
    try {
      const response = await fetch(`/locales/${locale}.json`);
      const messages = await response.json();
      i18n.global.setLocaleMessage(locale, messages);
    } catch (error) {
      console.error(`Failed to load locale ${locale}:`, error);
    }
  }

  i18n.global.locale.value = locale;
  localStorage.setItem('app_language', locale);
}

export default i18n;
