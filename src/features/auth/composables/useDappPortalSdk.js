import DappPortalSDK from '@linenext/dapp-portal-sdk'

let sdkInstance = null;

export const useDappPortalSdk = () => {
  const init = async () => {
    if (sdkInstance) return sdkInstance;

    if (!import.meta.env.VITE_DAPP_CLIENT_ID) {
      console.warn('[Missing Client ID]');
      return null;
    }

    sdkInstance = await DappPortalSDK.init({
      clientId: import.meta.env.VITE_DAPP_CLIENT_ID,
      chainId: '8217',
      testMode: true,
    });

    return sdkInstance;
  };

  const requestWalletAccess = async () => {
    const sdk = await init();
    const provider = sdk.getWalletProvider();
    const accounts = await provider.request({ method: 'kaia_requestAccounts' });
    return accounts?.[0] || null;
  };

  const getWalletAddress = async () => {
    const sdk = await init();
    const provider = sdk.getWalletProvider();
    const accounts = await provider.request({ method: 'kaia_accounts' });
    return accounts?.[0] || null;
  };

  const signMessage = async (message, address) => {
    const sdk = await init();
    const provider = sdk.getWalletProvider();
    return provider.request({
      method: 'personal_sign',
      params: [message, address],
    });
  };

  return {
    init,
    requestWalletAccess,
    getWalletAddress,
    signMessage,
  };
};
