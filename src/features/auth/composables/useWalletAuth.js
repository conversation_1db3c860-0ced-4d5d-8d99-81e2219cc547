// useWalletAuth.js
import { ref } from 'vue'
import { getAuthMessage, verifySignature } from '@/features/auth/api'
import { useToken } from '@/features/auth/composables/useToken'
import { useUser } from '@/features/auth/composables/useUser'
import { useDappPortalSdk } from '@/features/auth/composables/useDappPortalSDK'
import { useReferralBinding } from '@/features/referral'
import { useBoostLink } from '@/features/countdownChest'

const isConnected = ref(false)
const isProcessingCodes = ref(false)

export const useWalletAuth = () => {
  const { token, isTokenExpired, loadToken, setToken, clearToken } = useToken()
  const { user, loadUser, isWalletMatch, setUser, remove } = useUser()
  const { init, getWalletAddress, requestWalletAccess, signMessage } = useDappPortalSdk()
  const { bindCode: bindReferralCode } = useReferralBinding()
  const { processBoostCode } = useBoostLink()

  const loadConnection = async () => {
    await init()
    loadToken()
    loadUser()

    if (!token.value || isTokenExpired(token.value) || !user.value) {
      isConnected.value = false
      return false
    }

    try {
      const wallet = await getWalletAddress()
      const match = isWalletMatch(wallet)
      isConnected.value = !!(wallet && match)
      return isConnected.value
    } catch {
      isConnected.value = false
      return false
    }
  }

  const processShareCodes = async () => {
    if (isProcessingCodes.value) return
    isProcessingCodes.value = true

    try {
      // Get URL parameters
      const urlParams = new URLSearchParams(window.location.search)
      const referralCode = urlParams.get('ref')
      const boostCode = urlParams.get('boost')

      // Process referral code if present
      if (referralCode) {
        try {
          await bindReferralCode(referralCode)
          urlParams.delete('ref')
        } catch (error) {
          console.error('Failed to process referral code:', error)
        }
      }

      // Process boost code if present
      if (boostCode) {
        try {
          await processBoostCode(boostCode)
          urlParams.delete('boost')
        } catch (error) {
          console.error('Failed to process boost code:', error)
        }
      }

      // Update URL without the processed parameters
      const newUrl = window.location.pathname + (urlParams.toString() ? `?${urlParams.toString()}` : '')
      window.history.replaceState({}, '', newUrl)
    } finally {
      isProcessingCodes.value = false
    }
  }

  const connect = async () => {
    let wallet = await getWalletAddress()
    if (!wallet) {
      wallet = await requestWalletAccess()
    }

    const { message } = await getAuthMessage(wallet)
    const signature = await signMessage(message, wallet)
    const { token: newToken, user: newUser } = await verifySignature(wallet, message, signature)

    setToken(newToken)
    setUser(newUser)
    isConnected.value = true

    console.log("authenticated: ", { address: wallet, token: newToken, user: newUser })

    // Process share codes after successful connection
    await processShareCodes()

    return { address: wallet, token: newToken, user: newUser }
  }

  const disconnect = () => {
    clearToken()
    remove()
    isConnected.value = false
  }

  return {
    loadConnection,
    connect,
    disconnect,
    isConnected,
    processShareCodes
  }
}
