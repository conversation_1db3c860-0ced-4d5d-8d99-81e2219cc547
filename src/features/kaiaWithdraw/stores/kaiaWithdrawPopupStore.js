
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useKaiaWithdrawPopupStore = defineStore('kaiaWithdrawPopup', () => {

  const isKaiaWithdrawShow= ref(false)
  const openKaiaWithdrawPopup = () => (isKaiaWithdrawShow.value = true)
  const closeKaiaWithdrawPopup = () => (isKaiaWithdrawShow.value = false)

  return {
    isKaiaWithdrawShow,
    openKaiaWithdrawPopup,
    closeKaiaWithdrawPopup,
  }
})
