<script setup>
import { AppPopup, TitlePanelColor } from '@/components/common'
import { useKaiaWithdrawPopupStore } from '@/features/kaiaWithdraw'

const kaiaWithdrawPopupStore = useKaiaWithdrawPopupStore()


</script>

<template>
  <AppPopup :isOpen="kaiaWithdrawPopupStore.isKaiaWithdrawShow" @close="kaiaWithdrawPopupStore.closeKaiaWithdrawPopup">
    <TitlePanelColor class="title">KAIA Withdraw</TitlePanelColor>
  </AppPopup>
</template>

<style scoped>
</style>
