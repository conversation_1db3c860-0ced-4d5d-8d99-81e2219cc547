<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { useChestOverlayStore, useChestAssets } from '@/features/openChest'

const chestOverlay = useChestOverlayStore()
const { chestImages } = useChestAssets()
const { t } = useI18n()

defineProps<{
  step: 'initial' | 'opening' | 'opened' | string
}>()

const STAR_COUNT = 4
</script>


<template>
  <div class="centered-chest-container" @click="$emit('open')" v-if="['initial', 'opening', 'opened'].includes(step)">
    <!-- Stars -->
    <div class="stars-container">
      <div class="stars-row">
        <div 
          v-for="n in STAR_COUNT" 
          :key="`star-${n}`" 
          class="star star-placeholder"
          :class="{ 'star-active': step === 'opened' && n <= chestOverlay.currentChestConfig.chestRarity }"
          :style="step === 'opened' && n <= chestOverlay.currentChestConfig.chestRarity
            ? { animationDelay: `${(n - 1) * 0.2 + 0.1}s` }
            : {}"
        ></div>
      </div>
    </div>

    <!-- Chest Image -->
    <div class="chest-wrapper"
      :class="{
        'dark-filter': step === 'initial',
        'shake-animation': step === 'initial',
        'twitch-animation': step === 'initial',
        'pop-animation': step === 'opening'
      }"
    >
      <img 
        :src="step === 'initial' ? chestImages.closed : chestImages.opened"
        :alt="t('chest.image')" 
        class="chest-image"
      />
    </div>

    <!-- Instructions -->
    <div v-if="step === 'initial'" class="instruction-text">
      {{ t('chest.clickToOpen') }}
    </div>

    <!-- Next Button -->
    <div v-if="step === 'opened'" class="button-container">
      <button class="oc-button" @click.stop="$emit('next')">
        {{ t('button.next') }}
      </button>
    </div>
  </div>
</template>


<!-- Import shared styles globally within this component's scope -->
<style src="@/features/openChest/styles/shared.css"></style>

<!-- Scoped styles for this component only -->
<style scoped>
.centered-chest-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(var(--base-unit) * 260);
  width: 100%;
  max-width: calc(var(--base-unit) * 500);
}
.stars-container {
  position: absolute;
  top: calc(var(--base-unit) * -35);
  width: 100%;
  z-index: 5;
}
.stars-row {
  display: flex;
  justify-content: center;
  gap: calc(var(--base-unit) * 16);
  width: 100%;
}
.star {
  width: calc(var(--base-unit) * 35);
  height: calc(var(--base-unit) * 35);
  background-image: url('/icon/star.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}
.star-placeholder {
  opacity: 0.3;
  filter: grayscale(1) brightness(0.7);
}
.star-active {
  animation: starActivate 0.5s ease-out forwards;
}
@keyframes starActivate {
  0% {
    opacity: 0.3;
    filter: grayscale(1) brightness(0.7);
    transform: scale(1);
  }
  40% {
    opacity: 0.7;
    filter: grayscale(0.5) brightness(0.85);
    transform: scale(1.3);
  }
  70% {
    opacity: 1;
    filter: grayscale(0) brightness(1);
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    filter: grayscale(0) brightness(1);
    transform: scale(1);
  }
}

.chest-wrapper {
  width: calc(var(--base-unit) * 150);
  height: calc(var(--base-unit) * 150);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.chest-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: 0.3s ease-in-out;
}

.dark-filter {
  filter: saturate(0) brightness(0.2);
}
.shake-animation {
  animation: shake 0.1s ease-in-out infinite alternate;
}
@keyframes shake {
  0% { transform: rotate(-5deg); }
  100% { transform: rotate(5deg); }
}
.twitch-animation {
  animation: twitch 4s ease-in-out infinite;
}
@keyframes twitch {
  0%, 18%, 20%, 50%, 60%, 65%, 80%, 90%, 100% { transform: translate(0); }
  19% { transform: translate(calc(var(--base-unit) * 2), calc(var(--base-unit) * 2)); }
  62% { transform: translate(calc(var(--base-unit) * 2), calc(var(--base-unit) * -2)); }
  85% { transform: translate(calc(var(--base-unit) * 1), calc(var(--base-unit) * -1)); }
}
.pop-animation {
  animation: popEffect 0.4s ease-out;
}
@keyframes popEffect {
  0% {
    transform: scale(0.8);
    filter: saturate(0) brightness(0.2);
  }
  40% { transform: scale(1.1); }
  70% { transform: scale(0.95); }
  100% {
    transform: scale(1);
    filter: saturate(1) brightness(1);
  }
}
.instruction-text {
  position: absolute;
  bottom: calc(var(--base-unit) * -20);
  color: white;
  animation: pulse 2s infinite;
  text-align: center;
  width: 100%;
}
@keyframes pulse {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}
.button-container {
  position: absolute;
  bottom: calc(var(--base-unit) * -30);
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  animation: fadeIn 0.5s ease-out;
}

</style>