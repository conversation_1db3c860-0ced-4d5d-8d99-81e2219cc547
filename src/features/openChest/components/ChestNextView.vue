<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { useChestOverlayStore } from '@/features/openChest'
import { useChestAssets } from '@/features/openChest'

const { t } = useI18n()
const { chestImages } = useChestAssets()
const chestOverlay = useChestOverlayStore()

defineEmits(['next'])
</script>

<template>
  <div class="next-container" @click.stop>
    <h2 class="oc-heading">{{ t('chest.openAnotherChest') }}</h2>
    <div class="chest-image-container">
      <img :src="chestImages.closed" :alt="t('chest.nextChest')" class="chest-image" />
    </div>
    <div class="chest-buttons">
      <button class="oc-button" @click="$emit('next')">{{ t('chest.open') }}</button>
    </div>
  </div>
</template>

<!-- Import shared styles globally within this component's scope -->
<style src="@/features/openChest/styles/shared.css"></style>

<!-- Scoped styles for this component only -->
<style scoped>
.next-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.chest-image-container {
  width: calc(var(--base-unit) * 150);
  height: calc(var(--base-unit) * 150);
  display: flex;
  align-items: center;
  justify-content: center;
}

.chest-image {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  transition: 0.3s ease-in-out;
}

.chest-buttons {
  display: flex;
  margin-top: 1rem;
  justify-content: center;
}
</style>