<script setup>
import { onMounted, onUnmounted, computed, ref } from 'vue'
import { useCountdownChest } from '@/features/countdownChest'
import { useTapBoost } from '@/features/countdownChest/composables/useTapBoost'
import audioService from '@/lib/audioService'


const {
  tapCount,
  isTapped,
  showPopup,
  timeFlash,
  triggerTap
} = useTapBoost({
  canTap: () => progressPercent.value < 1,
  onTap: async () => {
    audioService.play('button1')
  },
  onAccelerate: async () => {
    await accelerate()
    audioService.play('star1')
  }
})

const {
  remaining,
  fetchData,
  openChest,
  accelerate,
  lastAcceleratedSeconds,
  isReadyToOpen,
  progressPercent,
  chestImage,
  chestTierClass,
  formatTime
} = useCountdownChest()

onMounted(fetchData)

</script>

<template>

  <div class="countdown-chest" @click="triggerTap">
    <div v-show="showPopup" class="popup-text">{{lastAcceleratedSeconds.toFixed(2)}}s</div>
    <div :class="[{ tapped: isTapped }]">
      <img :src="chestImage" alt="Countdown Chest" :class="['chest-image', chestTierClass]" />
    </div>
    <button
      class="progress-container"
      :class="{ ready: isReadyToOpen }"
      @click="isReadyToOpen ? openChest() : null"
    >
      <div class="progress-bar">
        <div
          class="progress-fill"
          :style="{ width: `${progressPercent * 100}%` }"/>
      </div>
      <div class="time-text" :class="{ flash: timeFlash }">
        {{ isReadyToOpen ? 'Open' : formatTime(remaining) }}
      </div>
    </button>

  </div>
</template>

<style scoped>
.popup-text {
  position: absolute;
  top: 30%;
  left: 50%;
  transform: translateX(-50%);
  color: #5EFF5C;
  font-weight: bold;
  font-size: calc(var(--base-unit) * 24);
  animation: floatUp 0.5s ease-out forwards;
  pointer-events: none;
  z-index: 5;

  -webkit-text-stroke: calc(var(--base-unit) * 2) black;
  text-shadow: 0 calc(var(--base-unit) * 2) calc(var(--base-unit) * 5) rgba(0, 0, 0, 0.32);
}

@keyframes floatUp {
  0% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
  80% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) translateY(-30px);
  }
}


.countdown-chest {
  position: relative;
  text-align: center;
  width: 100%;
  max-width: calc(var(--base-unit) * 160);
  margin: calc(var(--base-unit) * -28) auto 0 auto; 
}

.chest-image {
  width: 100%;
  height: auto;
}

@keyframes squashStretch {
  0% { transform: scale(1, 1); }
  30% { transform: scale(1.1, 0.9); }
  60% { transform: scale(0.95, 1.05); }
  100% { transform: scale(1, 1); }
}

.tapped {
  animation: squashStretch 0.2s ease;
}

@keyframes jiggleSoft {
  0%, 45% { transform: translateX(0); }
  50% { transform: translateX(-2px) translateY(-1px);}
  55% { transform: translateX(2px) translateY(1px);}
  60%, 100% { transform: translateX(0); }
}

@keyframes jiggleMedium {
  0%, 45% { transform: translateX(0); }
  50% { transform: translateX(-4px) translateY(-2px);}
  55% { transform: translateX(4px) translateY(2px);}
  60%, 100% { transform: translateX(0); }
}


@keyframes jiggleHard {
  0%, 100% { transform: rotate(0deg) scale(1); }
  5% { transform: rotate(-6deg) scale(1.05); }
  10% { transform: rotate(5deg) scale(1.03); }
  15% { transform: rotate(-4deg) scale(1.04); }
  20% { transform: rotate(4deg) scale(1.02); }
  25% { transform: rotate(-3deg) scale(1.03); }
  30% { transform: rotate(2deg) scale(1.01); }
  35% { transform: rotate(-2deg) scale(1.02); }
  40% { transform: rotate(1deg) scale(1); }
  50% { transform: rotate(0deg); }
}

@keyframes pulseOpen {
  0%, 40% { transform: scale(1); }
  50% { transform: scale(1.1); }
  60%, 100% { transform: scale(1); }
}

@keyframes pulseTap {
  0%, 40% { transform: scale(1, 1); }
  45% { transform: scale(1.12, 0.88); }
  50% { transform: scale(0.96, 1.04); }
  55%, 100% { transform: scale(1, 1); }
}

/* Animation classes with snappier timing */
.jiggle-1 {
  animation: jiggleSoft 2s infinite ease-out;
}

.jiggle-2 {
  animation: jiggleMedium 1.6s infinite ease-out;
}

.jiggle-3 {
  animation: jiggleHard 1.2s infinite ease-out;
}

.pulse {
  animation: pulseOpen 2s infinite ease-in-out;
}


.progress-container {
  margin: calc(var(--base-unit) * -22) auto calc(var(--base-unit) * -8) auto; 
  position: relative;
  width: calc(var(--base-unit) * 110);
  height: calc(var(--base-unit) * 30);
  background: #FF8A28;
  border-radius: calc(var(--base-unit) * 10);
  padding: calc(var(--base-unit) * 2);
  border: calc(var(--base-unit) * 2.5) solid #252635;
  overflow: hidden;
  display: flex;
  align-items: center;
  box-sizing: border-box;
}

.progress-container.ready {
  transition: transform 0.1s ease, box-shadow 0.1s ease;
}

.progress-container.ready:hover {
  transform: scale(1.02);
}

.progress-container.ready:active {
  transform: scale(0.98);
}

.progress-bar {
  flex: 1;
  height: 100%;
  background: transparent;
}

.progress-fill {
  height: 100%;
  background: #FDC844;
  transition: width 1s linear;
  border-radius: calc(var(--base-unit) * 8);
}


.time-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -45%);
  z-index: 1;
  color: #FFFFFF;

  font-size: calc(var(--base-unit) * 12);
  -webkit-text-stroke: calc(var(--base-unit) * 2) #3B3B3B;
  text-shadow: 0 calc(var(--base-unit) * 2) calc(var(--base-unit) * 5) rgba(0, 0, 0, 0.32);
  font-weight: bold;
  white-space: nowrap;
}

@keyframes flashGrow {
  0% { color: #5EFF5C; transform: translate(-50%, -45%) scale(1); }
  50% { transform: translate(-50%, -45%) scale(1.3); }
  100% { color: #FFFFFF; transform: translate(-50%, -45%) scale(1); }
}

.flash {
  animation: flashGrow 0.35s ease-in-out;
}

</style>
