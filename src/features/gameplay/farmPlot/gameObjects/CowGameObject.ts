import { Sprite, Assets } from 'pixi.js'; 
import { GameObject, Mathf, Vector2 } from '@/features/gameplay/shared';
import { Scene } from '@/features/gameplay/shared/scene';
import { SpriteRendererComponent } from '@/features/gameplay/shared/common';
import { AssetService } from '@/features/gameplay/farmPlot/services/AssetService';
import { CowRunnerComponent } from '@/features/gameplay/farmPlot/gameComponents/CowRunnerComponent';
import { IndexZSetterComponent } from '@/features/gameplay/farmPlot/gameComponents/IndexZSetterComponent';
import { CowProduceComponent } from '@/features/gameplay/farmPlot/gameComponents/CowProduceComponent';
import { CowBoostComponent } from '@/features/gameplay/farmPlot/gameComponents/CowBoostComponent';
import { useFarmStore } from '@/features/gameplay/farmPlot';

export class CowGameObject extends GameObject {
  constructor(scene: Scene, private plotNumber: number) {
    super(scene);
  }

  async start(): Promise<void> {
    this.transform.scale = new Vector2(0.15, 0.15);

    const assets = await AssetService.loadPlotAssets(this.plotNumber);
    const texture = await Assets.load(assets.cow);

    let spriteRenderer = new SpriteRendererComponent({
      texture,
      anchor: { x: 0.5, y: 0.5 },
    });

    this.addComponent(spriteRenderer);
    spriteRenderer.render(); // initial sync

    let cowRunner = new CowRunnerComponent();
    this.addComponent(cowRunner);
    cowRunner.randomizePosition();

    let indexZBasedOnY = new IndexZSetterComponent();
    this.addComponent(indexZBasedOnY);

    const farmStore = useFarmStore();
    const plotStats = farmStore.plots.find(p => p.plotNumber === this.plotNumber);
    if (!plotStats) return;

    let cowProduce = new CowProduceComponent(plotStats);
    this.addComponent(cowProduce);

    // Add boost component
    let cowBoost = new CowBoostComponent();
    this.addComponent(cowBoost);
  }

  override onUpdate(): void {

  }
}
