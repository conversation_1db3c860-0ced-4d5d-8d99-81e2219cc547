import { defineStore } from 'pinia'
import type { FarmPlotStats } from '@/features/gameplay/api';

export interface FarmPlotUnlockPopupState {
  isFarmPlotUnlockShow: boolean;
  plot: FarmPlotStats | null;
}

export const useFarmPlotUnlockPopupStore = defineStore('farmPlotUnlockPopup', {
  state: (): FarmPlotUnlockPopupState => ({
    isFarmPlotUnlockShow: false,
    plot: null,
  }),

  actions: {
    openFarmPlotUnlockPopup(selectedPlot: FarmPlotStats) {
      this.plot = selectedPlot;
      this.isFarmPlotUnlockShow = true;
    },

    closeFarmPlotUnlockPopup() {
      this.isFarmPlotUnlockShow = false;
    },
  },
})
