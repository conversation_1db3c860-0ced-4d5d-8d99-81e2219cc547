// stores/farmPlotUpgradePopupStore.ts
import { defineStore } from 'pinia';

export interface FarmPlotPopupState {
  isFarmPlotUpgradeShow: boolean;
  plotNumber: number | null;
}

export const useFarmPlotUpgradePopupStore = defineStore('farmPlotUpgradePopup', {
  state: (): FarmPlotPopupState => ({
    isFarmPlotUpgradeShow: false,
    plotNumber: null,
  }),

  actions: {
    openFarmPlotUpgradePopup(plotNumber: number) {
      this.plotNumber = plotNumber;
      this.isFarmPlotUpgradeShow = true;
    },

    closeFarmPlotUpgradePopup() {
      this.isFarmPlotUpgradeShow = false;
      this.plotNumber = null;
    },
  },
});
