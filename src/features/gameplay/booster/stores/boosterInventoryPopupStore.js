import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useBoosterInventoryPopupStore = defineStore('boosterInventoryPopup', () => {

  const isBoosterInventoryShow = ref(false)
  const openBoosterInventoryPopup = () => (isBoosterInventoryShow.value = true)
  const closeBoosterInventoryPopup = () => (isBoosterInventoryShow.value = false)

  return {
    isBoosterInventoryShow,
    openBoosterInventoryPopup,
    closeBoosterInventoryPopup,
  }
})
