<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { TitlePanelColor } from '@/components/common'
import { BoosterLabel, BoosterItem, BoosterPopup, BoosterTimeRemaining } from '@/features/gameplay/booster'
import { useBoosterInventoryPopupStore } from '../stores/boosterInventoryPopupStore'
import { useBoosterStore } from '@/features/gameplay/booster'
import audioService from '@/lib/audioService'
import { useBooster } from '@/features/iap/api'

const router = useRouter()
function goToIapShop() {
  audioService.play('button1')
  router.push("v1/shop")
}

const boosterInventoryPopupStore = useBoosterInventoryPopupStore()
const boosterStore = useBoosterStore()

const getBoosterImagePath = (productId: number): string => {
  const boosterImagePaths = {
    1: '/public/assets/booster/speed-boost-x2.png',
    2: '/public/assets/booster/speed-boost-x2.png',
    3: '/public/assets/booster/speed-boost-x4.png',
    4: '/public/assets/booster/speed-boost-x4.png',
    5: '/public/assets/booster/time-warp-1hr.png',
    6: '/public/assets/booster/time-warp-6hr.png',
    7: '/public/assets/booster/time-warp-24hr.png'
  }
  return boosterImagePaths[productId] || '/public/assets/booster/speed-boost-x2.png'
}

const handleUseBooster = async (boosterId: number) => {
  console.log('handleUseBooster', boosterId)
  await useBooster(boosterId)
  await boosterStore.refreshAll()
}

onMounted(async () => {
  await boosterStore.refreshAll()
})
</script>


<template>
  <BoosterPopup
    class="booster-inventory-popup"
    :isOpen="boosterInventoryPopupStore.isBoosterInventoryShow"
    @close="boosterInventoryPopupStore.closeBoosterInventoryPopup"
  >
    <TitlePanelColor class="title">Booster Inventory</TitlePanelColor>
    <div class="booster-inventory-content">
      <!-- Active Boosters Section -->
      <div class="booster-grid">
        <div
          v-for="booster in boosterStore.activeBoosters"
          :key="booster.id"
          class="boosted-status"
        >
          <div class="booster-item-content">
            <img
              class="booster-item-icon"
              :src="getBoosterImagePath(booster.product.id)"
              alt="booster-item-icon"
            />
          </div>
          <BoosterTimeRemaining :endTime="booster.endTime" />
        </div>
      </div>

      <!-- Inventory Section -->
      <BoosterLabel> Inventory </BoosterLabel>
      <div class="booster-grid">
        <BoosterItem
          :highlight="true"
          :labelText="'IAP SHOP'"
          :iconSrc="'/public/assets/booster/iap-store.png'"
          :buttonText="'Get'"
          @click="goToIapShop"
        >
          ddd
        </BoosterItem>

        <BoosterItem
          v-for="booster in boosterStore.boosters"
          :key="booster.id"
          :highlight="false"
          :labelText="booster.product.duration + ' hr'"
          :iconSrc="getBoosterImagePath(booster.product.id)"
          :buttonText="'Use'"
          @click="() => handleUseBooster(booster.id)"
        />
      </div>
    </div>
  </BoosterPopup>
</template>


<style scoped>
.title {
  margin-bottom: calc(var(--base-unit) * 12);
}

.booster-inventory-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: calc(var(--base-unit) * 12);
}

.booster-grid {
  background: #A1603C;
  border-radius: calc(var(--base-unit) * 8);
  border: calc(var(--base-unit) * 3) solid #D39B73;
  display: flex;
  justify-content: left;
  align-items: center;
  width: 100%;
  min-height: calc(var(--base-unit) * 100);
  padding: calc(var(--base-unit) * 12);
  box-sizing: border-box;
  gap: calc(var(--base-unit) * 12);
  overflow-x: auto;
  flex-wrap: nowrap;
  /* Prevent content from shrinking */
  min-width: 0;
}

.booster-grid :deep(.booster-item) {
  flex: 0 0 auto;
  min-width: calc(var(--base-unit) * 80);
}

.booster-item-label {
  color: #FDD99B;
  font-size: calc(var(--base-unit) * 12);
  padding: calc(var(--base-unit) * 4);
  text-shadow: none;
  text-align: center;
  margin-top: calc(var(--base-unit) * 4);
}

.booster-item-content {
  display: flex;
  justify-content: center;
  align-items: center;
}

.booster-item-icon {
  width: calc(var(--base-unit) * 64);
  height: calc(var(--base-unit) * 64);
  object-fit: contain;
  box-sizing: border-box;
}

.boosted-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}


</style>

