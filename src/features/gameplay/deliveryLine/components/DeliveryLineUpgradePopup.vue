<script setup>
import { ref, onMounted } from 'vue'
import { TitlePanelColor } from '@/components/common'
import { BoosterPopup, BoosterLabel} from '@/features/gameplay/booster'
import { useDeliveryLineUpgradePopupStore } from '@/features/gameplay/deliveryLine'
import { useDeliveryLineStore } from '@/features/gameplay/deliveryLine'
import { ColorButton } from '@/components/common'
import { useUserInfo } from '@/features/userInfo'
import { upgradeDeliveryLine } from '@/features/gameplay/api'
import audioService from '@/lib/audioService'

const { userInfo, fetchUserInfo } = useUserInfo();

const deliveryLineUpgradePopupStore = useDeliveryLineUpgradePopupStore()
const deliveryLineStore = useDeliveryLineStore()

const isLoading = ref(false)

const handleUpgrade = async () => {
  if (isLoading.value) return
  audioService.play('button1'); // Play sound effect
  try {
    isLoading.value = true
    await upgradeDeliveryLine()
    
    // Refresh data after successful upgrade
    await Promise.all([
      fetchUserInfo(),
      deliveryLineStore.fetchDeliveryLineFromApi()
    ])
  } catch (error) {
    console.error('Failed to upgrade delivery line:', error)
  } finally {
    isLoading.value = false
  }
}

onMounted(async () => {
  await fetchUserInfo();
  await deliveryLineStore.fetchDeliveryLineFromApi()
  console.log("deliveryLine",deliveryLineStore.deliveryLine)
})

const getDeliverySpeed = () => {
  return Math.floor(5 / deliveryLineStore.deliveryLine?.deliverySpeed * 100)
}

const getNextDeliverySpeedGrowth = () => {
  return Math.floor(5 / deliveryLineStore.deliveryLine?.nextUpgradeGrowth?.nextDeliverySpeed * 100) - getDeliverySpeed()
}

</script>

<template>
  <BoosterPopup class="upgrade-popup" :isOpen="deliveryLineUpgradePopupStore.isDeliveryLineUpgradeShow" @close="deliveryLineUpgradePopupStore.closeDeliveryLineUpgradePopup">
    <BoosterLabel class="title">Delivery Line</BoosterLabel>
    <div class="stage-title"> Level {{ deliveryLineStore.deliveryLine?.level }}</div>
    <div class="upgrade">
        <div class="upgrade-icon">
            <img class="icon-image" src="/public/assets/upgrade/milk-profit.png" alt="milk-profit-icon">
        </div>
        <div class="upgrade-info">
            <div class="upgrade-title">Milk profit</div>
            <div class="upgrade-stats">
              <div class="before">{{deliveryLineStore.deliveryLine?.blockPrice}}</div>
              <div class="after">+{{deliveryLineStore.deliveryLine?.nextUpgradeGrowth?.nextBlockPrice - deliveryLineStore.deliveryLine?.blockPrice}}</div>
            </div>
        </div>
    </div>
    <div class="upgrade">
        <div class="upgrade-icon">
            <img class="icon-image" src="/public/assets/upgrade/production-speed.png" alt="production-speed-icon">
        </div>
        <div class="upgrade-info">
            <div class="upgrade-title">Delivery speed</div>
            <div class="upgrade-stats">
              <div class="before">{{getDeliverySpeed()}}%</div>
              <div class="after">+{{getNextDeliverySpeedGrowth()}}%</div>
            </div>
        </div>
    </div>
    <div class="upgrade">
        <div class="upgrade-icon">
            <img class="icon-image" src="/public/assets/upgrade/milk-capacity.png" alt="milk-capacity-icon">
        </div>
        <div class="upgrade-info">
            <div class="upgrade-title">Milk capacity</div>
            <div class="upgrade-stats">
              <div class="before">{{deliveryLineStore.deliveryLine?.blockUnit}}</div>
              <div class="after">+{{deliveryLineStore.deliveryLine?.nextUpgradeGrowth?.nextBlockUnit - deliveryLineStore.deliveryLine?.blockUnit}}</div>
            </div>
        </div>
    </div>
    <div class="button-group-container">
        <div class="button-container">  
            <img class="button-icon" src="/public/assets/upgrade/upgrade.png" alt="upgrade-icon">
            <div class="button-text top">Upgrade</div>
        </div>
        <ColorButton 
          :disabled="userInfo.gem < deliveryLineStore.deliveryLine?.upgradeCost || isLoading" 
          :class="{ 'pulse-animation': !(userInfo.gem < deliveryLineStore.deliveryLine?.upgradeCost) }"
          @click="handleUpgrade"
        >
            <div class="button-container">
                <img class="button-icon" src="/public/icon/gem.png" alt="gem-icon">
                <div class="button-text">{{ isLoading ? 'Upgrading...' : `${deliveryLineStore.deliveryLine?.upgradeCost || 0} Gems` }}</div>
            </div>
        </ColorButton>
    </div>
  </BoosterPopup>
</template>

<style scoped>
.upgrade-popup {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.title {
    padding: calc(var(--base-unit) * 12);
    width: 100%;
    box-sizing: border-box;
    margin-bottom: calc(var(--base-unit) * 12);
    margin-top: calc(var(--base-unit) * -8);
    border-bottom: calc(var(--base-unit) * 2) solid #FCF1CB;
}

.stage-title {
  font-size: calc(var(--base-unit) * 16);
  font-weight: 600;
  text-align: center;
  margin-bottom: calc(var(--base-unit) * 12);
  color: #FCF1CB;
}

.upgrade {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: calc(var(--base-unit) * 12);
  margin-bottom: calc(var(--base-unit) * 12);
}

.upgrade-icon {
  background: radial-gradient(50% 50% at 50% 50%, #374DD7 18.27%, #A885AD 96.63%);
  border: calc(var(--base-unit) * 2) solid #0D2259;
  width: calc(var(--base-unit) * 56);
  height: calc(var(--base-unit) * 56);
  border-radius: calc(var(--base-unit) * 12);
  background-color: #FCF1CB;
  box-sizing: border-box;

  display: flex;
  align-items: center;
  justify-content: center;
  padding: calc(var(--base-unit) * 12);
}

.icon-image {
  width: calc(var(--base-unit) * 56);
  height: calc(var(--base-unit) * 56);
  object-fit: contain;
}

.upgrade-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #CF8E61;
  border: calc(var(--base-unit) * 3) solid #FDD99B;
  border-radius: calc(var(--base-unit) * 12);
  padding: calc(var(--base-unit) * 12) calc(var(--base-unit) * 12);
  width: 100%;
  height: calc(var(--base-unit) * 56);

  box-sizing: border-box;
  
  color:#FEEEC6;
  font-size: calc(var(--base-unit) * 14);
}

.upgrade-title {
  font-size: calc(var(--base-unit) * 16);
  font-weight: 600;
}

.upgrade-stats {
  display: flex;
  align-items: end;
  justify-content: space-between;
  flex-direction: column;
}

.before {
  color: #FEEEC6;
}

.after {
  color: #FDC844;
}


.button-group-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  gap: calc(var(--base-unit) * 12);
  margin: 0 auto;

  background: #FDC844;
  border: calc(var(--base-unit) * 1) solid #F0FFFF;
  border-radius: calc(var(--base-unit) * 12);
  width: calc(var(--base-unit) * 157);
  padding: calc(var(--base-unit) * 8) calc(var(--base-unit) * 16);
}

.button-icon {
  width: calc(var(--base-unit) * 24);
  height: calc(var(--base-unit) * 24);
  object-fit: contain;
}

.button-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: calc(var(--base-unit) * 8);
}

.button-text {
  transform: translateY(calc(var(--base-unit) * 2));
}

.button-text.top {
  font-weight: 700;
  font-size: calc(var(--base-unit) * 16);
}

@keyframes pulse-scale {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.pulse-animation {
  animation: pulse-scale 2s ease-in-out infinite;
}

</style>

