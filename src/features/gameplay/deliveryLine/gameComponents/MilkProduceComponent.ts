import { GameComponent, Vector2, GameObject } from '@/features/gameplay/shared';
import { MilkGameObject } from '@/features/gameplay/deliveryLine/gameObjects/MilkGameObject';
import { MilkRunnerComponent } from '@/features/gameplay/deliveryLine/gameComponents/MilkRunnerComponent';
import { DeliveryLineStats, DeliveryLineTemporalStats } from '@/features/gameplay/deliveryLine/stores/deliveryLineStore';
import { Ref } from 'vue';

export class MilkProduceComponent extends GameComponent {
  private timer = 0;
  private onSpawnCallback?: (milk: MilkGameObject) => void;

  constructor(private readonly deliveryLine: Ref<DeliveryLineStats>, private readonly temporalStats: Ref<DeliveryLineTemporalStats>) {
    super();
  }

  public get blockPrice(): number {
    return this.deliveryLine.value.blockPrice;
  }

  public get blockUnit(): number {
    return this.deliveryLine.value.blockUnit;
  }

  public get spawnInterval(): number {
    return this.deliveryLine.value.deliverySpeed / 5;
  }

  public get remainingMilk(): number {
    return this.deliveryLine.value.pendingMilk + this.temporalStats.value.temporallyMilk;
  }

  setOnMilkSpawned(callback: (milk: MilkGameObject) => void): void {
    this.onSpawnCallback = callback;
  }

  override update(deltaSeconds: number): void {
    this.timer += deltaSeconds;

    if (this.timer >= this.spawnInterval) {
      this.spawnMilk();
      this.timer = 0;
    }
  }

  private async spawnMilk(): Promise<void> {
    if (!this.gameObject) return;
    if (this.remainingMilk < this.blockUnit) return;

    this.temporalStats.value.temporallyMilk -= this.blockUnit;
    this.deliveryLine.value.pendingMilk -= this.blockUnit;

    const scene = this.gameObject.scene;
    const milk = await GameObject.instantiate(new MilkGameObject(scene)); 

    milk.transform.position = new Vector2(0.08, 0.68);

    // Add runner component
    const runner = new MilkRunnerComponent(this.blockPrice);
    milk.addComponent(runner);

    this.onSpawnCallback?.(milk);
  }

  override destroy(): void {
    this.timer = 0;
  }
}
