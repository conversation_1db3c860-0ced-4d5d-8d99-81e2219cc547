import { Vector2, GameComponent, GameObject } from '@/features/gameplay/shared';
import { MilkRunnerComponent } from './MilkRunnerComponent';
import { PopTextGameObject } from '@/features/gameplay/shared';
import { DeliveryLineStats } from '../stores/deliveryLineStore';
import { Ref } from 'vue';

export class ConveyorMoverComponent extends GameComponent {
  private targets: GameObject['transform'][] = [];

  constructor(private readonly deliveryLine: Ref<DeliveryLineStats>) {
    super();
  }

  addTarget(transform: GameObject['transform']): void {
    this.targets.push(transform);
  }

  override update(deltaSeconds: number): void {
    if (!this.gameObject) return;

    // Calculate how much to move in pixels this frame
    const unitPerSecond = 1 / this.deliveryLine.value.deliverySpeed;
    const moveBy = unitPerSecond * deltaSeconds;

    for (const transform of this.targets) {
      transform.position = new Vector2(
        transform.position.x + moveBy,
        transform.position.y
      );

      if(transform.position.x > 1) {
        transform.gameObject.getComponent(MilkRunnerComponent)?.onReachedTarget();
      }
    }
  }

  override destroy(): void {
    this.targets = [];
  }
}
