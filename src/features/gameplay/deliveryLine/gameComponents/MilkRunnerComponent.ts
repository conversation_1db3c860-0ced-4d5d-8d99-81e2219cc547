import { <PERSON>Component, Vector2, Mathf, GameObject, SpriteRendererComponent } from '@/features/gameplay/shared';
import { PopTextGameObject } from '@/features/gameplay/shared';
import { useDeliveryLineStore } from '@/features/gameplay/deliveryLine/stores/deliveryLineStore';

export class MilkRunnerComponent extends GameComponent {
  private _amount: number;

  constructor(amount: number = 1) {
    super();
    this._amount = amount;
  }

  get amount(): number {
    return this._amount;
  }

  public async onReachedTarget(): Promise<void> {
    const deliveryLineStore = useDeliveryLineStore();
    console.log(`[<PERSON><PERSON><PERSON><PERSON>] Milk reached target, amount: ${this._amount.toFixed(2)}`);

    deliveryLineStore.temporalStats.temporallyGem += this._amount;

    // 💬 Show "+milkAmount"
    const text = new PopTextGameObject(this.gameObject.scene, {
      text: `+${this._amount}`,
      scale: new Vector2(0.001, 0.0028),
      position: this.gameObject.transform.position.clone().add(new Vector2(-0.08, 0)),
      style: {
        fontSize: 52,
        fontWeight: 'bold',
        fill: '#00ff00',
        stroke: {
          color: '#000000',
          width: 6,
        },
      },
      moveDistance: 0.3,
    });

    await GameObject.instantiate(text);
    text.textObject.transform.parent = this.gameObject.transform.parent;
    this.gameObject.destroy(); // Triggers destroy hooks
  }
}
