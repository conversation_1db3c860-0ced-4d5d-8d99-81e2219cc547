// Scene.ts
import { Application, Container } from 'pixi.js';
import type { GameObject } from '@/features/gameplay/shared/core';
import { Vector2 } from '@esotericsoftware/spine-phaser';

export class Scene {
  public app!: Application;
  public container!: Container;
  private gameObjects: GameObject[] = [];
  public onResize: () => void = () => {};

  constructor(
    private containerRef: HTMLElement,
    private appOptions: ConstructorParameters<typeof Application>[0]
  ) {}

  async init(): Promise<void> {
    this.app = new Application();
    await this.app.init(this.appOptions);

    this.container = new Container();
    this.app.stage.addChild(this.container);

    this.containerRef.appendChild(this.app.canvas);

    this.app.ticker.add((ticker) => this.update(ticker.deltaMS / 1000));

    window.addEventListener('resize', () => this.handleResize());
    this.handleResize(); // Initial resize
  }

  add(go: GameObject): void {
    this.gameObjects.push(go);
  }

  remove(go: GameObject): void {
    const index = this.gameObjects.indexOf(go);
    if (index !== -1) {
      this.gameObjects.splice(index, 1);
    }
  }

  update(dt: number): void {
    for (const go of this.gameObjects) {
      go.update?.(dt);
    }
  }

  resize(): void {
    for (const go of this.gameObjects) {
      go.transform.resize?.();
    }
    this.onResize?.();
  }

  destroy(): void {
    for (const go of this.gameObjects) {
      go.destroy?.();
    }
    this.container.destroy({ children: true });
    this.app.destroy(true);
  }

  private handleResize(): void {
    const width = this.containerRef.offsetWidth;
    const height = this.containerRef.offsetHeight;
    this.app.renderer.resize(width, height);
    this.resize();
  }

  getSize(): Vector2 {
    return new Vector2(this.containerRef.offsetWidth, this.containerRef.offsetHeight);
  }
}
