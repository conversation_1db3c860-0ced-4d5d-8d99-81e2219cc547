import { Sprite, Texture } from 'pixi.js';
import { GameComponent } from '@/features/gameplay/shared/core';
import { GameObject } from '@/features/gameplay/shared/core';
import { Vector2 } from '@/features/gameplay/shared/generic';

export interface SpriteRendererOptions {
  texture: Texture;
  anchor?: { x: number; y: number };
  tint?: number;
  zIndex?: number;
}

export class SpriteRendererComponent extends GameComponent {
  public readonly sprite: Sprite;
  public scaleRelativeToWidth = true;

  constructor(private options: SpriteRendererOptions) {
    super();

    this.sprite = new Sprite(options.texture);
    this.sprite.anchor.set(options.anchor?.x ?? 0.5, options.anchor?.y ?? 0.5);

    if (options.tint !== undefined) this.sprite.tint = options.tint;
    if (options.zIndex !== undefined) this.sprite.zIndex = options.zIndex;
  }

  override attach(gameObject: GameObject): void {
    super.attach(gameObject);
    // Automatically add to the scene's main container
    this.gameObject.scene.container.addChild(this.sprite);

    // Initialize layout via transform
    this.gameObject.transform.resize();
    this.update(); // Initial transform sync
  }

  override update(): void {
    this.render();
  }
  


  render(): void {
    const { scale, rotation, position } = this.gameObject.transform;

    this.setSpritePosition(position);
    this.setSpriteScale(scale);

  }

  private setSpritePosition(pos: Vector2): void {
    if(this.gameObject.transform.parent) {
      const position = this.gameObject.transform.position;

      const localX = position.x * this.getParentSize().x;
      const localY = position.y * this.getParentSize().y;
      const x = this.getParentPosition().x + localX;
      const y = this.getParentPosition().y + localY;

      this.sprite.position.set(x, y);

      return;
    }

    let position = pos.multiply(this.getSceneSize())
    this.sprite.position.set(position.x, position.y);
  }

  private setSpriteScale(scale: Vector2): void {

    if(this.scaleRelativeToWidth) {
      let size = scale
      .divide(this.getTextureSize().x)
      .multiply(this.getParentSize().x)
      this.sprite.scale.set(size.x, size.y);
    }
    else {
      let size = scale
      .divide(this.getTextureSize().y)
      .multiply(this.getParentSize().y)
      this.sprite.scale.set(size.x, size.y);
    }
  }
  // size of the sprite 
  getSpriteSize(): Vector2 {
    return new Vector2(this.sprite.scale.x, this.sprite.scale.y);
  }
  
  // size of the texture px
  getTextureSize(): Vector2 {
    return new Vector2(this.sprite.texture.width, this.sprite.texture.height);
  }

  // size of the scene container
  getSceneSize(): Vector2 {
    if(!this.gameObject) return new Vector2(0, 0);
    return this.gameObject.scene.getSize();
  }

  getRawSize(): Vector2 {
    return new Vector2(this.sprite.width, this.sprite.height);
  }

  getParentSize(): Vector2 {
    if(!this.gameObject.transform.parent) return this.getSceneSize();
    if(!this.gameObject.transform.parent.gameObject.getComponent(SpriteRendererComponent)) return this.getSceneSize();
    return this.gameObject.transform.parent.gameObject.getComponent(SpriteRendererComponent).getRawSize();
  }

  getRawPosition(): Vector2 {
    return new Vector2(this.sprite.position.x, this.sprite.position.y);
  }

  getParentPosition(): Vector2 {
    if(!this.gameObject.transform.parent) return new Vector2(0, 0);
    if(!this.gameObject.transform.parent.gameObject.getComponent(SpriteRendererComponent)) return new Vector2(0, 0);
    return this.gameObject.transform.parent.gameObject.getComponent(SpriteRendererComponent).getRawPosition();
  }

  override destroy(): void {
    if (this.sprite.parent) {
      this.sprite.parent.removeChild(this.sprite);
    }
    this.sprite.destroy();
  }
}
