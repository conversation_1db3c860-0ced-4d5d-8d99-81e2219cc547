export type EaseFunction = (t: number) => number;

export const Easing = {
  Linear: (t: number): number => t,
  
  EaseInQuad: (t: number): number => t * t,
  EaseOutQuad: (t: number): number => t * (2 - t),
  
  EaseInOutQuad: (t: number): number => {
    t *= 2;
    if (t < 1) return 0.5 * t * t;
    t--;
    return -0.5 * (t * (t - 2) - 1);
  },

  EaseInCubic: (t: number): number => t * t * t,
  EaseOutCubic: (t: number): number => --t * t * t + 1,
};

export interface TweenConfig<T> {
  target: T;
  duration: number;
  delay?: number;
  ease?: EaseFunction;
  onUpdate?: (target: T, progress: number) => void;
  onComplete?: () => void;
  onStart?: () => void;
}

export class Tween<T> {
  private elapsedTime: number = 0;
  private delayTime: number = 0;
  private isComplete: boolean = false;
  private hasStarted: boolean = false;
  private ease: EaseFunction;
  private delay: number;

  constructor(private config: TweenConfig<T>) {
    this.ease = config.ease || Easing.Linear;
    this.delay = config.delay || 0;
  }

  update(deltaSeconds: number): void {
    if (this.isComplete) return;

    // Handle delay
    if (this.delayTime < this.delay) {
      this.delayTime += deltaSeconds;
      return;
    }

    // Call onStart when animation actually begins
    if (!this.hasStarted) {
      this.hasStarted = true;
      this.config.onStart?.();
    }

    this.elapsedTime += deltaSeconds;
    const progress = Math.min(this.elapsedTime / this.config.duration, 1);
    const easedProgress = this.ease(progress);

    if (this.config.onUpdate) {
      this.config.onUpdate(this.config.target, easedProgress);
    }

    if (progress >= 1) {
      this.isComplete = true;
      if (this.config.onComplete) {
        this.config.onComplete();
      }
    }
  }

  get completed(): boolean {
    return this.isComplete;
  }

  getTarget(): T {
    return this.config.target;
  }
} 