import { defineStore } from 'pinia';
import { useDeliveryLineStore } from '@/features/gameplay/deliveryLine';
import { useFarmStore } from '@/features/gameplay/farmPlot';
import { useUserInfo } from '@/features/userInfo';

export interface GameplayState {
  isActive: boolean;
  lastSyncTime: number;
  pendingChanges: boolean;
}

export const useGameplayStateManager = defineStore('gameplayState', {
  state: (): GameplayState => ({
    isActive: false,
    lastSyncTime: 0,
    pendingChanges: false,
  }),

  actions: {
    // Called when entering gameplay
    async enterGameplay() {
      this.isActive = true;
      this.lastSyncTime = Date.now();
      
      // Fetch initial state from API
      await this.syncFromServer();
      
      // Start periodic syncing
      this.startPeriodicSync();
    },

    // Called when exiting gameplay
    async exitGameplay() {
      this.isActive = false;
      
      // Stop periodic syncing
      this.stopPeriodicSync();
      
      // Final sync to server
      await this.syncToServer();
    },

    // Sync data from server
    async syncFromServer() {
      try {
        const deliveryLineStore = useDeliveryLineStore();
        const farmStore = useFarmStore();
        const { fetchUserInfo } = useUserInfo();

        await Promise.all([
          deliveryLineStore.fetchDeliveryLineFromApi(),
          farmStore.setFarmPlotsFromApi(),
          fetchUserInfo()
        ]);

        this.lastSyncTime = Date.now();
        this.pendingChanges = false;
      } catch (error) {
        console.error('Failed to sync from server:', error);
      }
    },

    // Sync data to server
    async syncToServer() {
      if (!this.pendingChanges) return;

      try {
        const deliveryLineStore = useDeliveryLineStore();
        const farmStore = useFarmStore();

        // Add API calls to update server state
        // await updateDeliveryLineState(deliveryLineStore.deliveryLine);
        // await updateFarmPlotsState(farmStore.plots);

        this.pendingChanges = false;
        this.lastSyncTime = Date.now();
      } catch (error) {
        console.error('Failed to sync to server:', error);
      }
    },

    // Mark that changes need to be synced
    markPendingChanges() {
      this.pendingChanges = true;
    },

    startPeriodicSync() {
      // Sync every 30 seconds while gameplay is active
      this.syncInterval = setInterval(async () => {
        if (this.isActive && this.pendingChanges) {
          await this.syncToServer();
        }
      }, 30000);
    },

    stopPeriodicSync() {
      if (this.syncInterval) {
        clearInterval(this.syncInterval);
        this.syncInterval = null;
      }
    }
  }
});