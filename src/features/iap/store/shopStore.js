import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getStoreProducts, createPayment } from '@/features/iap/api'
import { useNotificationStore } from '@/stores/notificationStore'
import { useDappPortalSdk } from '@/features/auth/composables/useDappPortalSDK'
import { useI18n } from 'vue-i18n'

export const useShopStore = defineStore('shop', () => {
  const { startPayment } = useDappPortalSdk()
  const { t } = useI18n()

  const products = ref([])
  const processing = ref(false)
  const paymentStep = ref('')


  const fetchProducts = async () => {
    try {
      const response = await getStoreProducts()
      products.value = response.products || []
      return products.value
    } catch (error) {
      const notificationStore = useNotificationStore()
      notificationStore.addNotification({
        type: 'error',
        message: 'Failed to load shop products'
      })
      throw error
    }
  }


  const resetStore = () => {
    products.value = []
    processing.value = false
    paymentStep.value = ''
  }


  const purchaseProduct = async (paymentData) => {
    if (processing.value) return
    processing.value = true
    paymentStep.value = 'creating'
    
    try {

      const response = await createPayment(paymentData)
      const paymentId = response?.paymentId
      
      if (!paymentId) {
        throw new Error('No payment ID received from server')
      }

      paymentStep.value = 'starting'
      
      const paymentResult = await startPayment(paymentId)
      
      paymentStep.value = 'completed'
      
      const notificationStore = useNotificationStore()
      notificationStore.addNotification({
        type: 'success',
        message: t('shop.payment.success', { orderId: paymentId })
      })
      
      return { ...response, paymentResult }
    } catch (error) {
      console.error('Purchase error:', error)
      paymentStep.value = 'failed'
      
      const notificationStore = useNotificationStore()
      
      let errorMessage
      if (error.code === -32000 && error.message === 'insufficient funds for transfer') {
        errorMessage = t('shop.payment.errors.insufficientFunds')
      } else if (error.code === -31001) {
        errorMessage = t('shop.payment.errors.canceled')
      } else if (error.code === -31002) {
        errorMessage = t('shop.payment.errors.failed')
      } else {
        errorMessage = error.message || t('shop.payment.errors.generic')
      }
      
      notificationStore.addNotification({
        type: 'error',
        message: errorMessage
      })
      throw error
    } finally {
      processing.value = false
      setTimeout(() => {
        paymentStep.value = ''
      }, 2000)
    }
  }

  const initializeShop = async () => {
    try {
      await fetchProducts()
    } catch (error) {
      console.error('Failed to initialize shop:', error)
    }
  }

  const cancelPayment = () => {
    processing.value = false
    paymentStep.value = ''
    
    const notificationStore = useNotificationStore()
    notificationStore.addNotification({
      type: 'info',
      message: t('shop.payment.canceled')
    })
  }

  return {
    // State
    products,
    processing,
    paymentStep,

    // Actions
    fetchProducts,
    purchaseProduct,
    resetStore,
    initializeShop,
    cancelPayment
  }
}) 