import fetch from '@/lib/fetch'

// Get all available products in the store
export const getStoreProducts = () => {
  // return fetch.get('/iap/store/products').then(res => res.data)
  return fetch.get('/iap/store/products')
}

// Create a payment for a product
export const createPayment = async ({ productId, imageUrl, paymentMethod, testMode = false }) => {
  try {
    const response = await fetch.post('/iap/payment/create', {
      productId,
      imageUrl,
      paymentMethod,
      testMode
    })
    
    return response.data || response
  } catch (error) {
    console.error('createPayment API error:', error)
    throw error
  }
}

// Get user's boosters (owned items)
export const getBoosters = () => {
  return fetch.get('/iap/boosters')
}

// Get active boosters
export const getActiveBoosters = () => {
  return fetch.get('/iap/boosters/active')
}

// Use a booster
export const useBooster = (boosterId) => {
  return fetch.post('/iap/boosters/use', { boosterId }).then(res => res.data)
}

// Get VIP membership status
export const getVipStatus = () => {
  return fetch.get('/iap/vip/status').then(res => res.data)
}

// Get purchase history with optional pagination
export const getPurchaseHistory = async (page = 1, limit = 20) => {
  try {
    const response = await fetch.get('/iap/purchase/history', {
      params: { page, limit }
    })
    
    if (response.data) {
      return response.data
    } else if (response.ok !== undefined) {
      return response
    } else {
      return response
    }
  } catch (error) {
    throw error
  }
} 