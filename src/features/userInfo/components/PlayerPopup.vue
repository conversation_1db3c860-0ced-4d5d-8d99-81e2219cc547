<script setup>
import { ColorButton, AppInput, AppPopup, TitlePanelColor } from '@/components/common'
import { usePlayerPopupStore } from '@/features/userInfo'
import { useUserInfo } from '@/features/userInfo'
import { updateUsername } from '@/features/userInfo'
import { ref } from 'vue'

const { userInfo, fetchUserInfo } = useUserInfo()
const playerPopupStore = usePlayerPopupStore()

const name = ref(userInfo?.username || '')

const onConfirm = async () => {
  if (!name.value || name.value === userInfo.value?.username) return
  await updateUsername(name.value)
  await fetchUserInfo()
  playerPopupStore.closePlayerPopup()
}


</script>

<template>
  <AppPopup :isOpen="playerPopupStore.showPlayerPopup" @close="playerPopupStore.closePlayerPopup">
    <div class="player-popup">
      <TitlePanelColor class="title">Edit Username</TitlePanelColor>
      <AppInput class="input" :placeholder="userInfo?.username || 'Enter name...'" v-model="name"/>
      <ColorButton class="button" @click="onConfirm">Confirm</ColorButton>
    </div>
  </AppPopup>
</template>

<style scoped>
.player-popup {
  color: white;
  text-align: center;
}

.title {
  font-size: calc(var(--base-unit) * 10);
  margin-bottom: calc(var(--base-unit) * 18);
}

.input {
  margin-bottom: calc(var(--base-unit) * 32);
  text-align: center;
}

.button {
  width: 100%;
}
</style>
