  <script setup>
  import { useI18n } from 'vue-i18n';
  import { AppModal } from '@/components/common';
  import { useDailyReferralReward } from '@/features/referral'
  import { ReferralProgressBar, DailyReferralRewardButton } from '@/features/referral';

  // i18n
  const { t } = useI18n();

  const { referralStatus } = useDailyReferralReward()

  </script>
  
<template>
    <AppModal class="info-container">
      <div class="info-text">{{ t('invite.infoText') }}</div>
  
      <div class="row-group target-text">
        <div class="target-box">{{ t('invite.dailyChestTargets', { count: 1 }) }}</div>
        <div class="target-box">{{ t('invite.dailyChestTargets', { count: 2 }) }}</div>
        <div class="target-box">{{ t('invite.dailyChestTargets', { count: 3 }) }}</div>
        <div class="target-box">{{ t('invite.dailyChestTargets', { count: 4 }) }}</div>
        <div class="target-box">{{ t('invite.dailyChestTargets', { count: 5 }) }}</div>
      </div>

      <ReferralProgressBar
        :value="referralStatus.currentReferrals"
        :milestones="[2, 5, 10, 20, 30]"
        :max="30"
      >
        <template #label="{ milestone }">
          {{ milestone }} {{ t('invite.user') }}
        </template>
      </ReferralProgressBar>

      <DailyReferralRewardButton />
    </AppModal>
  </template>

  
  <style scoped>
  .row-group {
    display: flex;
    gap: calc(var(--base-unit) * 10);
  }
  
  .target-text {
    color: rgba(153, 98, 0, 1);
    font-size: calc(var(--base-unit) * 10);
    line-height: calc(var(--base-unit) * 14);
    text-align: center;
  }
  
  .target-box {
    display: flex;
    align-items: center;
    width: calc(var(--base-unit) * 65);
    height: calc(var(--base-unit) * 45);
    padding: calc(var(--base-unit) * 4);
    background-color: rgba(251, 223, 99, 1);
    border-radius: calc(var(--base-unit) * 4);
    box-shadow: inset 0 0 0 calc(var(--base-unit) * 2) rgba(254, 246, 210, 1);
  }
  
  .info-container {
    padding: calc(var(--base-unit) * 24) calc(var(--base-unit) * 16);
  }
  
  .info-text {
    text-align: start;
    font-size: calc(var(--base-unit) * 14);
    line-height: calc(var(--base-unit) * 20);
  }
  </style>
  