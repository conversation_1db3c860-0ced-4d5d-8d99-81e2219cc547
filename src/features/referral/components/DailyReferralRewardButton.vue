<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useDailyReferralReward } from '@/features/referral'
import { ColorButton } from '@/components/common'

const { t } = useI18n()
const { isLoading, dailyAvailable, handleClaim } = useDailyReferralReward()

const buttonText = computed(() =>
  dailyAvailable.value ? t('button.claimReward') : t('chest.unavailable')
)
</script>

<template>
  <ColorButton
    :loading="isLoading"
    :disabled="!dailyAvailable"
    :class="{ 'disabled-button': !dailyAvailable }"
    @click="handleClaim"
  >
    {{ buttonText }}
  </ColorButton>
</template>

<style scoped>
.disabled-button {
  filter: grayscale(100%) brightness(0.6);
  cursor: not-allowed;
}
</style>
