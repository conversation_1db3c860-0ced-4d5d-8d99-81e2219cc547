import { computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useNotificationStore } from '@/stores/notificationStore'
import { useUserInfo } from '@/features/userInfo'
import audioService from '@/lib/audioService'

export function useReferralCode() {
  const { t } = useI18n()
  const notificationStore = useNotificationStore()
  const { userInfo, fetchUserInfo } = useUserInfo()

  const referralCode = computed(() => userInfo.value?.code || '')

  const generateReferralLink = (platform = 'kaia') => {
    if (!referralCode.value) return ''
    
    // Ensure platform is a string and has a value
    const platformStr = String(platform || 'kaia').toLowerCase()

    switch (platformStr) {
      case 'telegram':
        return `https://t.me/moofun_bot?startapp=r_${referralCode.value}`
      case 'kaia':
        return `https://moofun.app/referral?ref=${referralCode.value}`
      case 'line':
        return `https://liff.line.me/moofun?ref=${referralCode.value}`
      default:
        console.warn(`Unknown platform "${platform}", using default link format`)
        return `https://moofun.app/referral?ref=${referralCode.value}`
    }
  }

  const referralLink = computed(() => generateReferralLink())

  const copyToClipboard = async (platform = 'kaia') => {
    const link = generateReferralLink(platform)
    if (!link) return

    try {
      await navigator.clipboard.writeText(link)
      audioService.play('button1')

      notificationStore.addNotification({
        type: 'success',
        title: t('referral.linkCopied'),
        message: t('referral.linkCopiedToClipboard'),
        duration: 3000
      })
    } catch (err) {
      console.error(t('referral.failedToCopyError'), err)
      notificationStore.addNotification({
        type: 'error',
        message: t('referral.failedToCopyLink'),
        duration: 3000
      })
    }
  }

  onMounted(() => {
    fetchUserInfo()
  })

  return {
    referralCode,
    referralLink,
    generateReferralLink,
    copyToClipboard
  }
}
