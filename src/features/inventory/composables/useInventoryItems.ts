import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useUserInfo } from '@/features/userInfo'

export const useInventoryItems = (activeTab, selectedItemIndex) => {
  const { t } = useI18n()
  const { userInfo } = useUserInfo()

  const inventoryItems = computed(() => {
    const user = userInfo.value || {}

    return [
      { name: t('inventory.items.ticket.name'), key: 'ticket', icon: '/icon/premium-ticket.png', description: t('inventory.items.ticket.description') },
      { name: t('inventory.items.freeTicket.name'), key: 'free_ticket', icon: '/icon/free-ticket.png', description: t('inventory.items.freeTicket.description') },
      { name: t('inventory.items.gem.name'), key: 'gem', icon: '/icon/gem-glow.png', description: t('inventory.items.gem.description') },
      { name: t('inventory.items.kaia.name'), key: 'kaia', icon: '/icon/toncoin.webp', description: t('inventory.items.kaia.description') }
    ]
    .map(item => ({ ...item, quantity: user[item.key] || 0 }))
    .filter(i => i.quantity > 0)
  })

  const fragments = computed(() => {
    const user = userInfo.value || {}

    return [
      { name: t('inventory.items.fragments.green.name'), key: 'fragment_green', icon: '/icon/green-fragment.png', description: t('inventory.items.fragments.green.description') },
      { name: t('inventory.items.fragments.blue.name'), key: 'fragment_blue', icon: '/icon/blue-fragment.png', description: t('inventory.items.fragments.blue.description') },
      { name: t('inventory.items.fragments.purple.name'), key: 'fragment_purple', icon: '/icon/purple-fragment.png', description: t('inventory.items.fragments.purple.description') },
      { name: t('inventory.items.fragments.gold.name'), key: 'fragment_gold', icon: '/icon/gold-fragment.png', description: t('inventory.items.fragments.gold.description') }
    ]
    .map(item => ({ ...item, quantity: user[item.key] || 0 }))
    .filter(i => i.quantity > 0)
  })

  const visibleItems = computed(() => {
    const source = activeTab.value === 0 ? inventoryItems.value : fragments.value
    return [...source, ...Array(8 - source.length).fill({ key: null })]
  })

  const selectedItem = computed(() => {
    if (selectedItemIndex.value === null) return { description: t('inventory.slots.noItemSelected') }
    return visibleItems.value[selectedItemIndex.value]
  })

  const canCraft = computed(() => {
    if (activeTab.value !== 1 || selectedItemIndex.value === null || !selectedItem.value.key) return false

    const required = {
      fragment_green: 160,
      fragment_blue: 80,
      fragment_purple: 40,
      fragment_gold: 20
    }

    return selectedItem.value.quantity >= (required[selectedItem.value.key] || Infinity)
  })

  return {
    inventoryItems,
    fragments,
    visibleItems,
    selectedItem,
    canCraft
  }
}
