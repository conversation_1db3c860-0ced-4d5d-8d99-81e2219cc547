<script setup lang="ts">
import { ref, watch } from 'vue'
import audioService from '@/lib/audioService'

const props = defineProps<{
  tabs: { title: string }[]
  activeTab?: number
}>()

const emit = defineEmits<{
  (e: 'tab-change', index: number): void
}>()

const currentIndex = ref(props.activeTab ?? 0)

watch(() => props.activeTab, (newVal) => {
  if (typeof newVal === 'number') currentIndex.value = newVal
})

const selectTab = (index: number) => {
  audioService.play('button1')
  currentIndex.value = index
  emit('tab-change', index)
}
</script>

<template>
  <div class="tabs-container">
    <div
      v-for="(tab, index) in tabs"
      :key="index"
      class="tab-wrapper"
    >
      <div
        :class="['tab', { active: currentIndex === index }]"
        @click="selectTab(index)"
      >
        {{ tab.title }}
      </div>
      <div
        v-if="index < tabs.length - 1"
        class="tab-divider"
      />
    </div>
  </div>
</template>

<style scoped>
.tabs-container {
  display: flex;
  background-color: rgba(53, 54, 75, 1);
  border-radius: calc(var(--base-unit) * 6);
  padding: calc(var(--base-unit) * 2);
  gap: calc(var(--base-unit) * 4);
  align-items: center;
}

.tab-wrapper {
  display: flex;
  flex: 1;
  align-items: center;
}

.tab {
  flex: 1;
  text-align: center;
  padding: calc(var(--base-unit) * 4) 0;
  font-weight: bold;
  color: white;
  border-radius: calc(var(--base-unit) * 6);
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin: 0 calc(var(--base-unit) * 4);
  user-select: none;
  font-size: calc(var(--base-unit) * 12);
  padding-top: calc(var(--base-unit) * 6);
}

.tab-divider {
  width: calc(var(--base-unit) * 1);
  height: calc(var(--base-unit) * 12);
  background-color: rgba(150, 150, 150, 0.5);
  margin: 0 calc(var(--base-unit) * 4);
}

.tab.active {
  background-color: rgba(114, 221, 32, 1);
  color: white;
  border: solid calc(var(--base-unit) * 2) rgba(37, 38, 53, 1);
  -webkit-text-stroke: calc(var(--base-unit) * 2) rgba(64, 127, 16, 1);
}
</style>
