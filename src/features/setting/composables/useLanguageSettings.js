import { useI18n } from 'vue-i18n'
import { computed } from 'vue'
import { loadLocaleMessages } from '@/lib/i18n'
import audioService from '@/lib/audioService'

export const useLanguageSettings = () => {
  const { locale, t } = useI18n()

  const toggleLanguage = async () => {
    audioService.play('button1')
    const newLocale = locale.value === 'en' ? 'zh' : 'en'
    await loadLocaleMessages(newLocale)
  }

  const currentLanguageDisplay = computed(() =>
    locale.value === 'en' ? 'English' : '中文'
  )

  return {
    toggleLanguage,
    currentLanguageDisplay,
    t,
  }
}
