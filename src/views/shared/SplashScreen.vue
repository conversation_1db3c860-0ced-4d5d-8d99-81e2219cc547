<script setup>
import VersionText from '@/components/utility/VersionText.vue';
import Background from '@/components/common/Background.vue';

import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import audioService from '@/lib/audioService'

const router = useRouter()

let userInteracted = false

function handleUserInteraction() {
  if (!userInteracted) {
    userInteracted = true
    setTimeout(() => {
      audioService.playBGM()
    }, 500)
  }
}

onMounted(async () => {
  // Add interaction listeners
  document.addEventListener('click', handleUserInteraction)
  document.addEventListener('touchstart', handleUserInteraction)

  // Route to main menu after delay
  setTimeout(() => {
    router.push('/v1') 
  }, 3000)
})
</script>


<template>
  <div class="container">
    <Background :imagePath="'/img/intro.png'"/>
    <VersionText />
  </div>
</template>

<style scoped>
.container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}
</style>