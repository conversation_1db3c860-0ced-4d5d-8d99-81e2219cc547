<template>
    <div class="chest-container">
        <InfoPanel>
            <TitlePanelColor :variant="'green'">
                Lucky Draw
            </TitlePanelColor>
            <img class='chest-img' src='/icon/chest.png'>
            <div class="resources-container">
                <ResourceCard/>
                <ResourceCard 
                title="Ticket"
                imageSrc="/icon/ticket.png"
                bgColor="linear-gradient(-82deg, 
                    #71C983 0%,
                    #86EA9A 52%,
                    #5DE378 61%,
                    #8DEBA0 72%,
                    #337D42 100%
                )"
                borderColor="#000000"
                contentBg="#BCCFDF"
                iconBg="#999EA4"
                iconBorderColor="#728EA2"
                />
                <ResourceCard
                title="Ton"
                imageSrc="/icon/toncoin.webp"
                bgColor="#CDE8F4"
                borderColor="#000000"
                contentBg="#3CA4D8"
                iconBg="#CDE8F4"
                iconBorderColor="#048BCC"
                />
            </div>
        </InfoPanel>
        <HighlightButton :text="'Draw 1 times'"/>
    </div>
</template>

<script>
import TitlePanelColor from '../common/TitlePanelColor.vue'
import HighlightButton from '../ui/HighlightButton.vue';
import ResourceCard from '../vue/ResourceCard.vue';
import InfoPanel from '../ui/InfoPanel.vue'

export default {
name: 'ChestSection',
components: {
    ResourceCard,
    HighlightButton,
    InfoPanel,
    TitlePanelColor
}
}
</script>

<style scoped>
.chest-container {
    margin:  calc(var(--base-unit) * 10) calc(var(--base-unit) * 20);
    margin-bottom: calc(var(--base-unit) * 20);
    display: flex;
    flex-direction: column;
    gap: calc(var(--base-unit) * 16);
}


.chest-img {
    width: calc(var(--base-unit) * 137);
    height: calc(var(--base-unit) * 140);
    object-fit: contain; /* Ensures the image scales properly without distortion */
    align-self: center; /* Centers the image inside the flex container */
    margin-right: calc(var(--base-unit) * 8); /* Adjust this value as needed */
}

.resources-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: calc(var(--base-unit) * 16);
}

</style>