<template>
    <div class="wheel-container">

    <TitlePanelColor :variant="'purple'">
        Wheel Game
    </TitlePanelColor>
    <TimerPanel></TimerPanel>

    </div>
</template>

<script>
import InfoPanel from '../ui/InfoPanel.vue';
import TitlePanelColor from '../common/TitlePanelColor.vue';
import TimerPanel from '../common/TimerPanel.vue';

export default {
    name: 'WheelSection',
    components: {
        TitlePanelColor,
        InfoPanel,
        TimerPanel
    }
}
</script>

<style scoped>
  .wheel-container {
    margin:  calc(var(--base-unit) * 10) calc(var(--base-unit) * 20);
    margin-bottom: calc(var(--base-unit) * 20);
    display: flex;
    flex-direction: column;
    gap: calc(var(--base-unit) * 16);
}

img {
    width: calc(var(--base-unit) * 128);
    height: calc(var(--base-unit) * 128);
    object-fit: contain;
    align-self: center;
    margin-right: calc(var(--base-unit) * 8);
}

.text-container {
    margin: calc(var(--base-unit) * 20);
}

.bottom {
    display: flex;
    justify-content: center;
    align-items: center;
}

.bottom img {
    height: calc(var(--base-unit) * 28);
}
</style>