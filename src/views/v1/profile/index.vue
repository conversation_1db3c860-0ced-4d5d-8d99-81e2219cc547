<template>
  <div class="profile-container">
    <TitlePanelColor :variant="'purple'">
      {{ t('profile.title') }}
    </TitlePanelColor>

    <AppRow :iconSrc="'/icon/inventory.png'" :showArrow="true" :clickable="true" @click="goToinventory">
      <slot>{{ t('profile.items') }}</slot>
    </AppRow>

    <AppRow :iconSrc="'/icon/leaderboard.png'" :showArrow="true" :clickable="true" @click="goToLeaderboard">
      <slot>{{ t('profile.leaderboard') }}</slot>
    </AppRow>

    <TicketTransferRow/>
    <!--- <KaiaWithdrawRow/> -->


    <AppRow :iconSrc="'/icon/setting.png'" :showArrow="true" :clickable="true" @click="goToSetting">
      <slot>{{ t('profile.settings') }}</slot>
    </AppRow>
    
    <TicketTransferPopup/>
    <KaiaWithdrawPopup/>
  </div>
</template>
  
<script setup>
import { useI18n } from 'vue-i18n';
import { AppRow, HeaderLabel, TitlePanelColor } from '@/components/common';
import { TicketTransferRow, TicketTransferPopup } from '@/features/ticketTransfer';
import { KaiaWithdrawRow, KaiaWithdrawPopup } from '@/features/kaiaWithdraw';
import { useRouter } from 'vue-router'
import audioService from '@/lib/audioService'
  
const { t } = useI18n();
const router = useRouter()

const goToinventory = () => {
  audioService.play('button1'); // Play sound effect
  router.push("inventory")
};

const goToLeaderboard = () => {
  audioService.play('button1'); // Play sound effect
  router.push("leaderboard")
};

const goToSetting = () => {
  audioService.play('button1'); // Play sound effect
  router.push("profile/setting")
};

const openReferralDetails = () => {
  // Placeholder for future implementation
  audioService.play('button1'); // Play sound effect
  console.log('Open Referral Details');
  
};
</script>
  
  <style scoped>

  .profile-container {
    margin:  calc(var(--base-unit) * 10) calc(var(--base-unit) * 20);
    margin-bottom: calc(var(--base-unit) * 20);
    display: flex;
    flex-direction: column;
    gap: calc(var(--base-unit) * 16);
}


  </style>