  
<script setup>
import { GemCounter } from '@/features/userInfo'
import { DeliveryLinePanel, DeliveryLineUpgradePopup, DeliveryLine } from '@/features/gameplay/deliveryLine'
import { FarmGroup } from '@/features/gameplay/farmPlot'
import { BoosterInventoryPopup } from '@/features/gameplay/booster'
import { FarmPlotUpgradePopup } from '@/features/gameplay/farmPlot'
import { FarmPlotUnlockPopup } from '@/features/gameplay/farmPlot'
import { OfflineRewardPopup } from '@/features/gameplay/offline'
import TestBox from '@/features/gameplay/farmPlot/components/TestBox.vue'

</script>

<template>
  <div id="Home">
    <GemCounter></GemCounter>
    <DeliveryLinePanel/>
    <DeliveryLine/>
    <!-- <TestBox/> -->

    <FarmGroup/>
  </div>

  <BoosterInventoryPopup/>  
  <FarmPlotUpgradePopup/>
  <FarmPlotUnlockPopup/>
  <DeliveryLineUpgradePopup/>
  <OfflineRewardPopup/>
</template>

<style scoped>
.bottom {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
  position: absolute;
  bottom: calc(var(--base-unit) * 48);
}
</style>
