<script setup>
import { useI18n } from 'vue-i18n'
import { useReferralDetailsPopup } from '@/features/referral'

// Components
import {
  ReferralRewardCard,
  ReferralRewardButton,
  DailyReferralRewardPanel,
  ReferralCode,
  ReferralDetailsPopup
} from '@/features/referral'

import { CountdownChest } from '@/features/countdownChest'
import { TitlePanelColor, HeaderLabel, AppRow } from '@/components/common'

const { t } = useI18n()
const { isOpen, open, close } = useReferralDetailsPopup()

</script>

<template>
  <div class="invite-container">
    <TitlePanelColor :variant="'orange'">
      {{ t('invite.title') }}
    </TitlePanelColor>

    <CountdownChest />
    <ReferralRewardCard labelKey="invite.inviteFriend" :amount="1" />
    <ReferralRewardCard labelKey="invite.invitePremiumFriend" :amount="2" />
    <ReferralRewardButton />
    <DailyReferralRewardPanel />

    <AppRow :clickable="true" @click="open">
      <span class="left">{{ t('invite.referralDetails') }}</span>
      <template #right>
        <img class="arrow-icon" src="/ui/navigate-arrow-2.png" />
      </template>
    </AppRow>

    <ReferralCode />
    <ReferralDetailsPopup :isOpen="isOpen" @close="close" />
  </div>
</template>

  
  <style scoped>
  .arrow-icon {
    width: calc(var(--base-unit) * 20);
    height: calc(var(--base-unit) * 20);
    object-fit: contain;
}


  .invite-container {
    margin:  calc(var(--base-unit) * 10) calc(var(--base-unit) * 20);
    margin-bottom: calc(var(--base-unit) * 20);
    display: flex;
    flex-direction: column;
    gap: calc(var(--base-unit) * 16);
}
  </style>