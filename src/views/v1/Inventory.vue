<script setup lang="ts">
import { useRouter } from 'vue-router'
import audioService from '@/lib/audioService'
import { CloseButton, ColorButton, TitlePanelColor, AppModal } from '@/components/common'
import {
  InventoryGrid,
  InventoryTabs,
  InventoryDescription
} from '@/features/inventory'

import { useInventoryTabs } from '@/features/inventory'
import { useInventoryItems } from '@/features/inventory'
import { useCrafting } from '@/features/inventory'
import { GemCounter } from '@/features/userInfo'

const router = useRouter()
const { activeTab, selectedItemIndex, handleTabChange, selectItem } = useInventoryTabs()
const { visibleItems, selectedItem, canCraft } = useInventoryItems(activeTab, selectedItemIndex)
const { craftTicket } = useCrafting(selectedItem)

const closeSection = () => {
  audioService.play('button1')
  router.back()
}
</script>


<template>
  <div class="inventory-container">
    <CloseButton class="close-button" @click="closeSection" />
    <AppModal>
      <TitlePanelColor variant="green">
        {{ $t('inventory.title') }}
      </TitlePanelColor>
      <InventoryTabs
        :tabs="[
          { title: $t('inventory.tabs.items') },
          { title: $t('inventory.tabs.shards') }
        ]"
        :activeTab="activeTab"
        @tab-change="handleTabChange"
      />
      <InventoryGrid :items="visibleItems" :selectedIndex="selectedItemIndex" @select="selectItem" />
      <InventoryDescription :description="selectedItem?.description" :emptyText="$t('inventory.slots.emptySlot')"/>
      <ColorButton @click="craftTicket" :disabled="!canCraft" :class="{ 'disabled-button': !canCraft }">
        {{ $t('inventory.buttons.craft') }}
      </ColorButton>
    </AppModal>
  </div>
</template>

<style scoped>
.inventory-container {
  height: calc(var(--base-unit) * 500);
  margin: calc(var(--base-unit) * 20) calc(var(--base-unit) * 20);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: calc(var(--base-unit) * 16);
  position: relative;
}

.disabled-button {
  filter: grayscale(100%) brightness(0.6);
  cursor: not-allowed;
}

.close-button {
  position: absolute;
  top: calc(var(--base-unit) * -15);
  right: calc(var(--base-unit) * -15);
  z-index: 10;
}
</style>
