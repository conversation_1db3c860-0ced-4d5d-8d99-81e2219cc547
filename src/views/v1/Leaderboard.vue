<script setup lang="ts">
import { useRouter } from 'vue-router'
import audioService from '@/lib/audioService'
import { CloseButton, AppModal } from '@/components/common'
import { GemLeaderboard } from '@/features/leaderboard'

const router = useRouter()

const closeSection = () => {
  audioService.play('button1')
  router.back()
}
</script>

<template>
  <div class="leaderboard-container">
    <CloseButton class="close-button" @click="closeSection" />
    <AppModal class="modal">
      <GemLeaderboard />
    </AppModal>
  </div>
</template>

<style scoped>
.leaderboard-container {
  margin: calc(var(--base-unit) * 20) calc(var(--base-unit) * 30);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: calc(var(--base-unit) * 16);
  position: relative;
}

.modal {
  width: 100%;
}

.close-button {
  position: absolute;
  top: calc(var(--base-unit) * -15);
  right: calc(var(--base-unit) * -25);
  z-index: 10;
}
</style>
