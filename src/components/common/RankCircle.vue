<template>
    <div class="rank-circle-wrapper">
      <div class="rank-circle-container" :style="backgroundStyle">
        <span class="rank-text">{{ rank }}</span>
      </div>
    </div>
  </template>
  
  <script>
  export default {
    name: 'RankCircle',
    props: {
      rank: {
        type: Number,
        default: 1
      },
      imageUrl: {
        type: String,
        required: true
      }
    },
    computed: {
      backgroundStyle() {
        return {
          background: `url(${this.imageUrl}) no-repeat center center`,
          backgroundSize: 'contain'
        };
      }
    }
  };
  </script>
  
  <style scoped>
  .rank-circle-wrapper {
    position: relative;
    width: calc(var(--base-unit) * 24);
    height: calc(var(--base-unit) * 24);
  }
  
  .rank-circle-container {
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .rank-text {
    position: relative;
    font-size: calc(var(--base-unit) * 14);
    font-weight: bold;
    color: rgba(255, 247, 170, 1);
    -webkit-text-stroke: calc(var(--base-unit) * 1) rgba(0, 0, 0, 1);
  }
  </style>