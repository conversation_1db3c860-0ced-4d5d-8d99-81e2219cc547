<template>
  <div class="label-container" :style="{ background: backgroundColor }">
    <span class="label">
      <slot></slot>
    </span>
  </div>
</template>

<script>
import { defineComponent } from 'vue';
//rgba(106, 93, 229, 1) - ongoing purple
//rgba(9, 165, 81, 1) - completed green

export default defineComponent({
  name: 'RoundedLabel',
  props: {
    backgroundColor: {
      type: String,
      default: 'rgba(9, 165, 81, 1)'
    }
  }
});
</script>

<style scoped>
.label-container {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  height: calc(var(--base-unit) * 24);
  width: fit-content;
  padding: calc(var(--base-unit) * 4) calc(var(--base-unit) * 8);
  border-radius: calc(var(--base-unit) * 8);
  box-sizing: border-box;
}

.label {
  color: rgba(254, 253, 250, 1);
  font-size: calc(var(--base-unit) * 12);
  font-weight: bold;
  text-shadow: none;
  -webkit-text-stroke: 0;
}
</style>