<template>
    <div class="panel-container">
      <div class="pattern-overlay"></div>
      <div class="content-wrapper">
        <slot></slot>
      </div>
    </div>
  </template>
  
  <script>
  import { defineComponent } from 'vue';
  
  export default defineComponent({
    name: 'DecoratedTitlePanel'
  });

  
  </script>
  
  <style scoped>
  .panel-container {
    position: relative;
    width: calc(var(--base-unit) * 343);
    height: calc(var(--base-unit) * 48);
    background: rgba(229, 79, 1, 1);
    transform: skew(-10deg);
    padding: calc(var(--base-unit) * 8) calc(var(--base-unit) * 16);
    box-sizing: border-box;
    border-radius: calc(var(--base-unit) * 8);
    border: calc(var(--base-unit) * 4) solid black;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  /* Background pattern */
  .pattern-overlay {
    position: absolute;
    right: 0;
    top: 0;
    width: calc(var(--base-unit) * 100);
    height: 100%;
    background: url('/ui/right-pattern.png') no-repeat right center;
    background-size: contain;
    pointer-events: none;
  }
  
  .content-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    transform: skew(10deg);
    width: 100%;
    color: white;
    font-size: calc(var(--base-unit) * 18);
    -webkit-text-stroke: calc(var(--base-unit) * 1) rgba(59, 59, 59, 1);
    text-shadow: none;
  }
  </style>