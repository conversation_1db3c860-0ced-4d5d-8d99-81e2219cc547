<script>
export default {
name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
props: {
    text: {
    type: String,
    default: 'Default Label Text'
    }
}
}
</script>

<template>
      <div class="banner-container">
        <div class="banner-content">
          <div class="deco deco--left"></div>
          <div class="main-text"> {{ text }}</div>
          <div class="deco deco--right"></div>
        </div>
      </div>
</template>

  
<style scoped>
.banner-container {
    position: relative;
    width: 100%;
    height: calc(var(--base-unit) * 18);
}

.banner-content {
position: relative;
display: flex;
justify-content: center;
align-items: center;
width: 100%;
height: 100%;
gap: calc(var(--base-unit) * 16);
}

.deco {
width: calc(var(--base-unit) * 50);
height: 100%;
background-size: contain;
background-position: center;
background-repeat: no-repeat;
}

.deco--left {
background-image: url('/ui/header-deco-left.png');
}

.deco--right {
background-image: url('/ui/header-deco-right.png');
}
</style>