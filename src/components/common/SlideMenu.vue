<template>
    <transition name="fade">
      <div v-if="isOpen" class="dark-overlay" @click="$emit('close')"></div>
    </transition>
  
    <transition name="slide">
      <div v-if="isOpen" class="menu-overlay">
        <div class="menu-container">
          <div class="menu-items">
            <div 
              v-for="(item, index) in menuItems" 
              :key="index" 
              class="menu-item"
              @click="handleItemClick(item)"
            >
              <span class="menu-text main-text">{{  item.label }}</span>
              <img class="arrow-container" src="/ui/navigate-arrow.png" alt="Arrow"/>
            </div>
          </div>
          <div class="close-container">
            <button @click="$emit('close')" class="close-button">
              <img src="/ui/close-button.png" alt="Close" class="close-image"/>
            </button>
          </div>
        </div>
      </div>
    </transition>
  </template>
  

  <script>
  export default {
    name: 'SlideMenu',
    props: {
      isOpen: Boolean,
      menuItems: {
        type: Array,
        default: () => [
          { label: 'Moo Holder Leaderboard', onClick: () => console.log('Leaderboard clicked') },
          { label: 'Moo Throne Jackpot Pool', onClick: () => console.log('Jackpot Pool clicked') },
          { label: 'Global Individual Influencer Pool', onClick: () => console.log('Influencer Pool clicked') },
          { label: 'Global Team Influencer Pool', onClick: () => console.log('Team Pool clicked') }
        ]
      }
    },
    emits: ['close'], // Explicitly declare 'close' as an emitted event
    methods: {
      handleItemClick(item) {
        if (typeof item.onClick === 'function') {
          item.onClick();
        }
        this.$emit('close'); // Optional: Close the menu after clicking an item
      }
    }
  };
  </script>
  


<style scoped>
.menu-overlay {
position: fixed;
top: 0;
left: 0;
height: 100vh;
z-index: 1000;
}

.menu-container {
width: calc(var(--base-unit) * 246);
height: 100%;
background: rgba(172, 198, 232, 0.9);
display: flex;
flex-direction: column;
justify-content: center;
}

.menu-item {
transition: background-color 0.3s;
border-bottom: calc(var(--base-unit) * 1) solid rgba(179, 179, 179, 1); /* Default border color */
display: flex;
align-items: center;
justify-content: space-between;
padding: calc(var(--base-unit) * 14) calc(var(--base-unit) * 16);
}

.menu-item:active {
background: rgba(253, 200, 68, 1);
border-bottom-color: black; /* Change border color on hover */    
border-bottom-width: calc(var(--base-unit) * 2);
}

/* Apply padding only to the text */
.menu-text {
display: inline-block;
line-height: calc(var(--base-unit) * 24);
width: calc(var(--base-unit) * 148);
}

.close-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: calc(var(--base-unit) * 12);
}

.close-button {
    display: flex;
    justify-content: center;
    align-items: center;
    background: none;
    border: none;
    transition: transform 0.15s ease-out;
}

.close-button:active {
    transform: scale(0.98);
}

.close-image {
    width: calc(var(--base-unit) * 48);
    height: calc(var(--base-unit) * 48);
    object-fit: contain;
}

.arrow-container {
  width: calc(var(--base-unit) * 24);
  height: calc(var(--base-unit) * 24);
  object-fit: contain;

}

/* Slide animation */
.slide-enter-active, .slide-leave-active {
transition: transform 0.3s ease;
}

.slide-enter-from, .slide-leave-to {
transform: translateX(-100%);
}

.slide-enter-to, .slide-leave-from {
transform: translateX(0);
}

/* Add styles for the dark overlay */
.dark-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1;
}

/* Fade animation for overlay */
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}


</style>
