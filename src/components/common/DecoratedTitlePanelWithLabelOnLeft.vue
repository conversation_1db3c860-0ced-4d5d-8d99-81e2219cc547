<template>
    <div class="counter-container">
      <DecoratedTitlePanel class="points-panel">
        <!-- Slot for the panel content -->
        <slot name="panel-content">
          <!-- Default content if no slot provided -->
          <span class="value-container">0 PTS</span>
        </slot>
      </DecoratedTitlePanel>
    
      <LabelRed class="banner-label">
        <!-- Slot for the label content -->
        <slot name="label-content">
          <!-- Default content if no slot provided -->
          TOTAL PTS
        </slot>
      </LabelRed>
    </div>
  </template>
    
  <script setup>
  import { LabelRed, DecoratedTitlePanel } from '@/components/common'

  </script>
    
  <style scoped>
  .counter-container {
    position: relative;
    height: calc(var(--base-unit) * 61);
    display: flex;
    justify-content: center;
    align-items: center;
  }
    
  .points-panel {
    margin-top: calc(var(--base-unit) * 13);
  }
    
  /* Position the label component */
  .banner-label {
    position: absolute;
    top: 0;
    left: calc(var(--base-unit) * 24);
  }
    
  /* Value display styling - kept for default slot content */
  .value-container {
    color: white;
    font-size: calc(var(--base-unit) * 18);
    font-weight: bold;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    -webkit-text-stroke: calc(var(--base-unit) * 2) #3B3B3B;
  }
  </style>