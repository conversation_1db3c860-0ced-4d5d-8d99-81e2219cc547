<template>
    <div class="button-container">
      <div class="button-inner flex-center">
        <span class="button-text">{{ text }}</span>
      </div>
    </div>
  </template>
  
  <script>
  export default {
    props: {
      text: {
        type: String,
        required: true,
        default: "Click Me"
      }
    }
  };
  </script>
  
  <style scoped>
  .button-container {
    position: relative;
    height: calc(var(--base-unit) * 51);
    transition: transform 0.15s ease-out;

    border-radius: calc(var(--base-unit) * 12);
    border: calc(var(--base-unit) * 4) solid #202020;

    background-image: linear-gradient(to bottom, #FFD702 0%, #FF9200 100%);
    padding: calc(var(--base-unit) * 8);
    box-sizing: border-box;
  }
  .button-container:active {
    transform: scale(0.98);
  }
  
  .button-inner {
    z-index: 2;
    background-image: linear-gradient(to bottom, #FFA801 0%, #FFB002 100%);
    height: 100%;
    width: 100%;

    border-radius: calc(var(--base-unit) * 8);
  }

  .button-text {
    -webkit-text-stroke-color: #6D6D6D;
    position: absolute;
    z-index: 3;
  }
  </style>
  