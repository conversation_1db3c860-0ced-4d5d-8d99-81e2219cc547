<!-- LeaderboardList.vue -->
<template>
    <div class="leaderboard-list">
      <div class="header-row">
        <div class="rank">Rank</div>
        <div class="name">Name</div>
        <div class="score"><PERSON><PERSON>(Times)</div>
      </div>
      <div class="scrollable-container">
        <div v-for="(player, index) in players" 
             :key="player.name" 
             class="player-row"
             :class="{ 'alternate': index % 2 === 0 }">
             <RankCircle :rank="1" imageUrl="ui/rank-1-circle.svg" />
          <div class="name">{{ player.name }}</div>
          <div class="score">{{ player.score }}</div>
        </div>
      </div>
    </div>
  </template>
  
  <script>
import { RankCircle } from '@/components/common';

  export default {
    name: 'LeaderboardList',
    components: {
      RankCircle
    },
    data() {
      return {
        players: [
          { rank: 1, name: '<PERSON>', score: 15 },
          { rank: 2, name: '<PERSON><PERSON>', score: 12 },
          { rank: 3, name: '<PERSON>', score: 10 },
          { rank: 4, name: '<PERSON>', score: 7 },
          { rank: 5, name: '<PERSON>', score: 6 },
          { rank: 6, name: '<PERSON>', score: 4 },
          { rank: 7, name: 'Lena', score: 2 },
          { rank: 8, name: 'Bull', score: 1 }
        ]
      }
    }
  }
  </script>
  
  <style scoped>
* {
    font-size: calc(var(--base-unit) * 10);
}

  .leaderboard-list {
    background: #606780;
    border: calc(var(--base-unit) * 4) solid #7692BC;
    border-radius: calc(var(--base-unit) * 24);
    overflow: hidden;
    width: 100%;

    box-sizing: border-box;
  }
  
  .header-row {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    padding: calc(var(--base-unit) * 8) calc(var(--base-unit) * 24);
    color: white;
    font-weight: bold;
  }
  
  .scrollable-container {
    max-height: calc(var(--base-unit) * 300);
    overflow-y: auto;
  }
  
  .player-row {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    padding: calc(var(--base-unit) * 8) calc(var(--base-unit) * 24);
    color: white;
  }
  
  .player-row.alternate {
    background: #7692BC;
  }
  
  .rank, .name, .score {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  

  
  /* Scrollbar styling */
  .scrollable-container::-webkit-scrollbar {
    width: calc(var(--base-unit) * 8);
  }
  
  .scrollable-container::-webkit-scrollbar-track {
    background: rgba(71, 85, 105, 0.3);
  }
  
  .scrollable-container::-webkit-scrollbar-thumb {
    background: rgba(148, 163, 184, 0.5);
    border-radius: calc(var(--base-unit) * 4);
  }
  
  .scrollable-container::-webkit-scrollbar-thumb:hover {
    background: rgba(148, 163, 184, 0.7);
  }
  </style>