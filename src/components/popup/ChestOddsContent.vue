<template>
    <div class="popup-html-content">
        <div v-html="$t('countdownChestPopup.oddContent')" />
    </div>
  </template>
  
  <script setup>
  defineProps({ data: Object });
  </script>
  
  <style scoped>
  .popup-html-content {
    font-size: calc(var(--base-unit) * 14);
    line-height: 1.6;
    color: #4b5974;
    max-height: calc(var(--base-unit) * 240);
    overflow-y: auto;
    padding-right: calc(var(--base-unit) * 2);
  }
  
  .popup-html-content::-webkit-scrollbar {
    width: 6px;
  }
  
  .popup-html-content::-webkit-scrollbar-thumb {
    background-color: rgba(75, 89, 116, 0.5);
    border-radius: calc(var(--base-unit) * 3);
  }
  
  ul {
    padding-left: calc(var(--base-unit) * 12);
    margin-bottom: calc(var(--base-unit) * 2);
  }
  
  .chest-rarity-list li {
    margin-bottom: calc(var(--base-unit) * 2);
  }
  
  .note {
    font-style: italic;
    color: #7a7a7a;
    margin-top: calc(var(--base-unit) * 10);
  }
  </style>
  