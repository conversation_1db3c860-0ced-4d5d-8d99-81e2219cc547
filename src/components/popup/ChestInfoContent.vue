<template>
    <div class="popup-html-content">
        <div v-html="$t('countdownChestPopup.infoContent')" />
    </div>
  </template>
  
  <script setup>
  defineProps({ data: Object });
  </script>
  
  <style scoped>



  .popup-html-content {
    font-size: calc(var(--base-unit) * 14);
    line-height: 1.6;
    color: #4b5974;
    max-height: calc(var(--base-unit) * 240);
    overflow-y: auto;
    padding-right: calc(var(--base-unit) * 2);
  }

  /* ✅ Custom thin scrollbar */
.popup-html-content::-webkit-scrollbar {
  width: calc(var(--base-unit) * 4);
}

.popup-html-content::-webkit-scrollbar-thumb {
  background-color: rgba(75, 89, 116, 0.5);
  border-radius: calc(var(--base-unit) * 3);
}

  
  .popup-html-content ul {
    padding-left: calc(var(--base-unit) * 12);
    margin-bottom: calc(var(--base-unit) * 2);
  }
  
  .popup-html-content li {
    margin-bottom: calc(var(--base-unit) * 2);
  }
  
  .note {
    font-style: italic;
    color: #7a7a7a;
    margin-top: calc(var(--base-unit) * 12);
  }
  </style>
  
  <style>
/* Global, unscoped version of the table styles */
.promo-boost-table {
    width: 100%;
    border-collapse: collapse;
    margin: calc(var(--base-unit) * 4) 0;
    font-size: calc(var(--base-unit) * 13);
}

.promo-boost-table th,
.promo-boost-table td {
    border: calc(var(--base-unit) * 1) solid #4b5974;
    padding: calc(var(--base-unit) * 2);
    text-align: center;
}

.promo-boost-table th {
    background-color: rgba(45, 114, 193, 1);
    color: white;
}
</style>
