<template>
  <div ref="gameContainer" class="phaser-container"></div>
</template>

<script setup>
import { onMounted, onBeforeUnmount, ref } from 'vue'
import Phaser from 'phaser'
import { SpinePlugin } from '@esotericsoftware/spine-phaser'
import CowScene from "../../scenes/CowScene.js"

// Game reference
const gameContainer = ref(null)
let game;

onMounted(() => {
  if (gameContainer.value) {
    // Initialize Phaser
    const config = {
      type: Phaser.AUTO,
      parent: gameContainer.value, // Set the correct parent container
      width: window.innerWidth,
      height: window.innerHeight,
      backgroundColor: 0x000000, // No background color (transparent)
      transparent: true,  // Ensure the Phaser game canvas is transparent
      scale: {
        mode: Phaser.Scale.RESIZE,
        autoCenter: Phaser.Scale.Center
      },
      scene: [CowScene],
      plugins: {
        scene: [
          { 
            key: 'SpinePlugin',
            plugin: SpinePlugin,
            mapping: 'spine'
          }
        ]
      }
    };

    game = new Phaser.Game(config);
  }
})

onBeforeUnmount(() => {
  if (game) {
    game.destroy(true);
    game = null;
  }
})
</script>

<style scoped>
.phaser-container {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: block;
  overflow: hidden;
  pointer-events: none;
}
</style>
