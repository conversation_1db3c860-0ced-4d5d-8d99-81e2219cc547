<template>
  <div class="jackpot-popup-container">
    <AppModal class="info-panel" v-if="isOpen" @close="close">
      <div class="jackpot-content">
        <div class="jackpot-title">
          {{ $t('jackpotPopup.title') }}
        </div>
        <img :src="chestImage" alt="Jackpot Chest" class="chest-image" />

        <div class="rarity">{{ $t('jackpotPopup.rarity') }}</div>
        <div class="message" v-html="$t('jackpotPopup.message')"></div>
      </div>
    </AppModal>
    <div>
      <button class="share-btn main-text" @click="accept">
        {{ $t('jackpotPopup.acceptButton') }}
      </button>
    </div>
  </div>
</template>


<script setup>
import { AppModal } from '@/components/common'

const props = defineProps({
  isOpen: Boolean,
  chestImage: {
    type: String,
    default: '/img/jackpot-chest.png'
  }
});

const emit = defineEmits(['close', 'accept']);

const close = () => emit('close');
const accept = () => emit('accept');
</script>

<style scoped>
.jackpot-popup-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: calc(var(--base-unit) * 26);
  gap: calc(var(--base-unit) * 10);
  
}

.jackpot-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: calc(var(--base-unit) * 8);
}

.jackpot-title {
  font-size: calc(var(--base-unit) * 28);
  color: rgba(75, 89, 116, 1);
  -webkit-text-stroke: calc(var(--base-unit) * 1) rgba(147, 167, 202, 1);
}

.jackpot-popup-container .info-panel {
  padding: calc(var(--base-unit) * 26);
}

.chest-image {
  width: calc(var(--base-unit) * 226);
}



.rarity {
  font-weight: bold;
  font-size: calc(var(--base-unit) * 24);
  padding: calc(var(--base-unit) * 4) calc(var(--base-unit) * 24);
  border-radius: calc(var(--base-unit) * 16);
  background-color: rgba(139, 180, 228, 1);

  width: calc(100% - calc(var(--base-unit) * 52));
  margin-left: calc(var(--base-unit) * -26);
  margin-right: calc(var(--base-unit) * -26);
}

.highlight {
  color: #ff7a00;
}

.message {
  line-height: calc(var(--base-unit) * 20);
  font-size: calc(var(--base-unit) * 16);
  color: rgba(75, 89, 116, 1);
  -webkit-text-stroke: calc(var(--base-unit) * 1) rgba(147, 167, 202, 1);
}

.share-btn {
    background-color: rgba(10, 189, 93, 1);
    box-shadow: 0px calc(var(--base-unit) * -4) 0px rgba(8, 3, 10, 0.33) inset;
    border: none;
    border-radius: calc(var(--base-unit) * 8);
    padding: calc(var(--base-unit) * 14) calc(var(--base-unit) * 10);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.15s ease-out;
    font-size: calc(var(--base-unit) * 14);
    color: white;
    cursor: pointer;
}

/* Active/Click effect */
.share-btn:active {
    transform: scale(0.98);
}
</style>
