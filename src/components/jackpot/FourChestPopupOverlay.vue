<template>
  <div class="four-chest-overlay" v-if="showPopup" @click="handleOverlayClick">
    <div class="four-chest-popup-container" @click.stop>
      <AppModal class="info-panel" @close="close">
        <div class="four-chest-content">
          <div class="four-chest-title">
            {{ $t('fourChestPopup.title') }}
          </div>
          <div class="chest-wrapper" :class="{'pop-animation': animateChest}">
            <img :src="chestImage" alt="Four Chest" class="chest-image" />
          </div>
          <div class="rarity">{{ $t('fourChestPopup.rarity') }}</div>
          <div class="message" v-html="$t('fourChestPopup.message')"></div>
        </div>
      </AppModal>
      <div class="button-container">
        <button class="share-btn main-text" @click="handleAccept" :disabled="loading">
          {{ loading ? $t('fourChestPopup.loading') : $t('fourChestPopup.acceptButton') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { AppModal } from '@/components/common';
import { useJackpotChestStore } from '@/stores/fourChestStore';
import { useNotificationStore } from '@/stores/notificationStore.js';
import { useChestOverlayStore } from '@/features/openChest'
import { computed, onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';

const props = defineProps({
  chestImage: {
    type: String,
    default: '/img/chest-with-crystals.png'
  }
});

const emit = defineEmits(['close']);

const { t } = useI18n();
const jackpotStore = useJackpotChestStore();
const notificationStore = useNotificationStore();
const chestOverlayStore = useChestOverlayStore();
const loading = ref(false);
const animateChest = ref(false);

// Load status on mount
onMounted(() => {
  jackpotStore.fetchFourChestsStatus();
  // Start chest animation after a small delay
  setTimeout(() => {
    animateChest.value = true;
  }, 300);
});

const showPopup = computed(() => jackpotStore.hasCollectedFourChests.value === false);

const close = () => emit('close');

const handleAccept = async () => {
  try {
    loading.value = true;
    // Call the store method to collect the chest
    const response = await jackpotStore.collectFourChests();

    // Check if the collection was successful
    if (response) {
      // Format the response for the chest overlay
      const chestOverlayData = {
        ok: true,
        data: response.result
      };

      // Show the chest overlay animation
      chestOverlayStore.openChest(chestOverlayData);

      // Show success notification
      notificationStore.addNotification({
        type: 'success',
        title: t('notification.chestCollected'),
        message: t('notification.checkInventory'),
        duration: 3000
      });
    } else {
      // Show error notification
      notificationStore.addNotification({
        type: 'error',
        message: t('notification.failedToCollect'),
        duration: 3000
      });
    }
  } catch (error) {
    console.error('Error collecting four chests:', error);
    
    // Show error notification
    notificationStore.addNotification({
      type: 'error',
      message: t('notification.errorCollecting'),
      duration: 3000
    });
  } finally {
    loading.value = false;
    emit('close'); // hide popup after collecting (regardless of success/failure)
  }
};

// Close when clicking outside the popup content
const handleOverlayClick = (event) => {
  // Only close if clicking directly on the overlay
  if (event.target.classList.contains('four-chest-overlay')) {
    close();
  }
};
</script>

<style scoped>
.four-chest-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.four-chest-popup-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: calc(var(--base-unit) * 26);
  gap: calc(var(--base-unit) * 10);
  max-width: 90%;
  animation: slideUp 0.4s ease-out;
}

@keyframes slideUp {
  from { 
    transform: translateY(calc(var(--base-unit) * 30));
    opacity: 0;
  }
  to { 
    transform: translateY(0);
    opacity: 1;
  }
}

.four-chest-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: calc(var(--base-unit) * 8);
}

.four-chest-title {
  font-size: calc(var(--base-unit) * 28);
  color: rgba(75, 89, 116, 1);
  -webkit-text-stroke: calc(var(--base-unit) * 1) rgba(147, 167, 202, 1);
}

.four-chest-popup-container .info-panel {
  padding: calc(var(--base-unit) * 26);
}

.chest-wrapper {
  position: relative;
  width: calc(var(--base-unit) * 226);
  height: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pop-animation {
  animation: popEffect 0.6s ease-out;
}

@keyframes popEffect {
  0% {
    transform: scale(0.8);
    opacity: 0.7;
  }
  40% {
    transform: scale(1.1);
  }
  70% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.chest-image {
  width: 100%;
  height: auto;
  object-fit: contain;
  transition: 0.3s ease-in-out;
}

.rarity {
  font-weight: bold;
  font-size: calc(var(--base-unit) * 24);
  padding: calc(var(--base-unit) * 8) calc(var(--base-unit) * 24);
  border-radius: calc(var(--base-unit) * 16);
  background-color: rgba(139, 180, 228, 1);
  width: calc(100% - calc(var(--base-unit) * 32));
  margin-left: calc(var(--base-unit) * -16);
  margin-right: calc(var(--base-unit) * -16);
}

.highlight {
  color: #ff7a00;
}

.message {
  line-height: calc(var(--base-unit) * 20);
  font-size: calc(var(--base-unit) * 16);
  color: rgba(75, 89, 116, 1);
  -webkit-text-stroke: calc(var(--base-unit) * 1) rgba(147, 167, 202, 1);
}

.button-container {
  margin-top: calc(var(--base-unit) * 10);
  animation: fadeIn 0.5s ease-out 0.3s both;
}

.share-btn {
  background-color: rgba(10, 189, 93, 1);
  box-shadow: 0px calc(var(--base-unit) * -4) 0px rgba(8, 3, 10, 0.33) inset;
  border: none;
  border-radius: calc(var(--base-unit) * 8);
  padding: calc(var(--base-unit) * 14) calc(var(--base-unit) * 10);
  min-width: calc(var(--base-unit) * 120);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  font-size: calc(var(--base-unit) * 14);
  color: white;
  cursor: pointer;
}

/* Hover effect */
.share-btn:hover:not(:disabled) {
  transform: scale(1.05);
  box-shadow: 0 0 calc(var(--base-unit) * 10) rgba(10, 189, 93, 0.5);
}

/* Active/Click effect */
.share-btn:active:not(:disabled) {
  transform: scale(0.98);
}

.share-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}
</style>