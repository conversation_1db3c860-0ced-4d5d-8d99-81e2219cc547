import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { telegramService } from '@/services';

export const useTelegramStore = defineStore('telegram', () => {
    // State
    const isInitialized = ref(false);
    const authData = ref(null);
    const rawInitData = ref(null);
    const referralCode = ref(null);
    const error = ref(null);

    // Computed
    const user = computed(() => authData.value?.user || null);
    const userId = computed(() => user.value?.id || null);
    const userName = computed(() => user.value?.username || '');
    const firstName = computed(() => user.value?.first_name || '');
    const lastName = computed(() => user.value?.last_name || '');
    const languageCode = computed(() => user.value?.language_code || 'en');
    const isPremium = computed(() => user.value?.is_premium || false);

    // Actions
    const initTelegram = async () => {
        try {
            error.value = null;
            
            if (!telegramService.isTelegramReady()) {
                throw new Error('Telegram WebApp SDK not available');
            }

            const data = await telegramService.initTelegram();
            
            if (data) {
                authData.value = data;
                rawInitData.value = telegramService.getRawInitData();
                isInitialized.value = true;
                
                // Extract referral code from start_param if present
                if (data.start_param) {
                    // Handle different referral code formats
                    if (data.start_param.startsWith('r_')) {
                        referralCode.value = data.start_param.substring(2); // Remove 'r_' prefix
                    } else if (data.start_param.startsWith('b_')) {
                        // This might be a boost code, handle differently if needed
                        console.log('Boost code detected:', data.start_param);
                    } else {
                        referralCode.value = data.start_param;
                    }
                }
                
                console.log('Telegram store initialized successfully');
                return data;
            } else {
                throw new Error('Failed to initialize Telegram');
            }
        } catch (err) {
            console.error('Error initializing Telegram store:', err);
            error.value = err.message;
            isInitialized.value = false;
            return null;
        }
    };

    const clearReferralCode = () => {
        referralCode.value = null;
    };

    const reset = () => {
        isInitialized.value = false;
        authData.value = null;
        rawInitData.value = null;
        referralCode.value = null;
        error.value = null;
    };

    return {
        // State
        isInitialized,
        authData,
        rawInitData,
        referralCode,
        error,
        
        // Computed
        user,
        userId,
        userName,
        firstName,
        lastName,
        languageCode,
        isPremium,
        
        // Actions
        initTelegram,
        clearReferralCode,
        reset
    };
});
