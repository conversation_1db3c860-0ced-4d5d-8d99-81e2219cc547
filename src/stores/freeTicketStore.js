// stores/freeTicketStore.js
import { defineStore } from 'pinia';
import { freeTicketService } from '@/services';
import { useNotificationStore } from '@/stores/notificationStore.js';
import { ref } from 'vue';

export const useFreeTicketStore = defineStore('freeTicketStore', () => {
    const notificationStore = useNotificationStore();

  // State
  const transferResult = ref(null)
  const remainingLimit = ref(null)

  // Actions
  const transferFreeTickets = async (toWalletAddress, amount) => {
    try {
        const result = await freeTicketService.transferFreeTickets(toWalletAddress, amount)
        transferResult.value = result

        console.log("DD", result);

        if (result) 
        {
            if (result.ok) 
            {
                notificationStore.addNotification({
                type: 'success',
                message: result.message,
                duration: 3000
                });
            } else {
                notificationStore.addNotification({
                type: 'error',
                message: result.message,
                duration: 3000
                });
            }
        } 
    } catch (err) {
        console.error('Failed to transfer tickets:', err)
        transferResult.value = null
    }
}

  const getRemainingTransferLimit = async () => {
    try {
      const result = await freeTicketService.getRemainingTransferLimit()
      remainingLimit.value = result
    } catch (err) {
      console.error('Failed to fetch remaining limit:', err)
      remainingLimit.value = null
    }
  }

  return {
    // State
    transferResult,
    remainingLimit,

    // Actions
    transferFreeTickets,
    getRemainingTransferLimit,
  }
})
