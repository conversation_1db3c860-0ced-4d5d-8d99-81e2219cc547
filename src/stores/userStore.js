import { defineStore } from 'pinia';
import { userService } from '@/services';

export const useUserStore = defineStore('user', {
  state: () => ({
    loading: false,
    error: null,
    userData: null // full raw object
  }),

  getters: {
    isLoggedIn: (state) => !!state.userData,
    username: (state) => state.userData?.username || '',
    fullName: (state) => `${state.userData?.firstName || ''} ${state.userData?.lastName || ''}`.trim(),
    walletAddress: (state) => state.userData?.walletAddress || '',
    telegramPremium: (state) => state.userData?.telegram_premium || false,
    email: (state) => state.userData?.email || '',

    // Currencies & items
    gem: (state) => state.userData?.gem || 0,
    ton: (state) => state.userData?.ton || 0,
    usd: (state) => state.userData?.usd || 0,
    ticket: (state) => state.userData?.ticket || 0,
    freeTicket: (state) => state.userData?.free_ticket || 0,

    // Moofs
    moof: (state) => state.userData?.moof || 0,
    unlockedMoof: (state) => state.userData?.unlockMoof || 0,

    // Fragments
    fragmentGreen: (state) => state.userData?.fragment_green || 0,
    fragmentBlue: (state) => state.userData?.fragment_blue || 0,
    fragmentPurple: (state) => state.userData?.fragment_purple || 0,
    fragmentGold: (state) => state.userData?.fragment_gold || 0,

    referralCode: (state) => state.userData?.code || '',
    referralWallet: (state) => state.userData?.referral?.walletAddress || null,
    joinedDate: (state) => state.userData?.createdAt || '',
  },

  actions: {
    async fetchUserData() {
      this.loading = true;
      this.error = null;

      try {
        const result = await userService.getCurrentUser();
        if (result) {
          this.userData = result[0]; // based on your API response
        } else {
          this.userData = null;
          throw new Error('User not found or unauthorized.');
        }
      } catch (err) {
        console.error('userStore fetchUserData error:', err);
        this.error = err.message;
        this.userData = null;
      } finally {
        this.loading = false;
      }
    },

    logout() {
      this.userData = null;
      this.error = null;
      this.loading = false;
    },

    updateUserField(key, value) {
      if (this.userData && Object.prototype.hasOwnProperty.call(this.userData, key)) {
        this.userData[key] = value;
      }
    }
  }
});
