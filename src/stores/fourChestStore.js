import { defineStore } from 'pinia';
import { ref } from 'vue';
import { jackpotService } from '@/services'; // Adjust path as needed

export const useJackpotChestStore = defineStore('jackpotChest', () => {
  // State
  const hasCollectedFourChests = ref(false);
  const isLoading = ref(false);
  const rewards = ref(null);
  const errorMessage = ref(null);

  // Actions
  const fetchFourChestsStatus = async () => {
    isLoading.value = true;
    errorMessage.value = null;

    try {
      const status = await jackpotService.getFourJackpotChestsStatus();
      hasCollectedFourChests.value = status;
    } catch (error) {
      console.error('Error fetching status:', error);
      errorMessage.value = 'Failed to fetch chest status.';
    } finally {
      isLoading.value = false;
    }
  };

  const collectFourChests = async () => {
    isLoading.value = true;
    errorMessage.value = null;

    try {
      const result = await jackpotService.collectFourJackpotChests();
      if (result) {
        rewards.value = result;
        hasCollectedFourChests.value = true;
        return result;
      } else {
        errorMessage.value = 'Failed to collect chests.';
      }
    } catch (error) {
      console.error('Error collecting chests:', error);
      errorMessage.value = 'An error occurred while collecting chests.';
    } finally {
      isLoading.value = false;
    }
  };

  return {
    hasCollectedFourChests,
    isLoading,
    rewards,
    errorMessage,
    fetchFourChestsStatus,
    collectFourChests,
  };
});
