import { defineStore } from 'pinia';
import { ref } from 'vue';

export const useTaskStore = defineStore('task', () => {
    // State
    const tasks = ref([]);
    const loading = ref(false);
    const error = ref(null);

    // Actions
    const fetchTasks = async () => {
        try {
            loading.value = true;
            error.value = null;
            
            // TODO: Implement actual task fetching logic
            // For now, just return empty array to prevent errors
            tasks.value = [];
            
            console.log('Tasks fetched successfully');
        } catch (err) {
            console.error('Error fetching tasks:', err);
            error.value = err.message;
        } finally {
            loading.value = false;
        }
    };

    const completeTask = async (taskId) => {
        try {
            // TODO: Implement task completion logic
            console.log('Task completed:', taskId);
        } catch (err) {
            console.error('Error completing task:', err);
            throw err;
        }
    };

    const reset = () => {
        tasks.value = [];
        loading.value = false;
        error.value = null;
    };

    return {
        // State
        tasks,
        loading,
        error,
        
        // Actions
        fetchTasks,
        completeTask,
        reset
    };
});
