// stores/popupOverlayStore.js
import { defineStore } from 'pinia';
import { ref } from 'vue';

export const usePopupOverlayStore = defineStore('popupOverlay', () => {
  const isOpen = ref(false);
  const type = ref(null); // 'info', 'odds', etc.
  const title = ref('');
  const payload = ref(null); // dynamic data

  function open(typeValue, titleValue = '', data = null) {
    type.value = typeValue;
    title.value = titleValue;
    payload.value = data;
    isOpen.value = true;
  }

  function close() {
    isOpen.value = false;
    type.value = null;
    payload.value = null;
  }

  return { isOpen, type, title, payload, open, close };
});
