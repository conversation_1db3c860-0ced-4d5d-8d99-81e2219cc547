import { defineStore } from 'pinia';
import { ref, computed, watch, inject } from 'vue';

import { telegramService, tonService, tokenService, tonProofService, referralService } from '@/services';
import { useNotificationStore } from '@/stores/notificationStore.js';
import { useTelegramStore } from '@/stores/telegramStore.js';
import { useUserStore } from '@/stores/userStore';
import { useTaskStore } from '@/stores/taskStore';
import { useI18n } from 'vue-i18n';

import { toUserFriendlyAddress } from "@tonconnect/ui";

export const useTonStore = defineStore('ton', () => {
    // Get i18n instance
    const i18n = inject('i18n');
    const { t } = useI18n();
    
    // State
    const walletAddress = ref(null);
    const connectionStatus = ref('disconnected'); // 'disconnected', 'connecting', 'connected', 'failed'
    const lastError = ref(null);
    const notificationStore = useNotificationStore();
    const telegramStore = useTelegramStore();
    const referralProcessed = ref(false);

    // Import user store
    const userStore = useUserStore();  // Access userStore to handle logout

    // Import task store
    const taskStore = useTaskStore();

    // Computed
    const formattedAddress = computed(() => {
        if (!walletAddress.value) return '';
        
        const addr = walletAddress.value;
        const prefixLength = 6;
        const suffixLength = 6;
        
        if (addr.length <= prefixLength + suffixLength + 3) {
            return addr;
        }
        
        return `${addr.slice(0, prefixLength)}...${addr.slice(-suffixLength)}`;
    });

    const isConnected = computed(() => 
        !!walletAddress.value && connectionStatus.value === 'connected'
    );

    // Methods
    const init = () => {
        // Set up wallet status change handler
        tonService.onStatusChange(handleWalletStatusChange);
        
        // Check for existing valid connection
        if (tokenService.hasValidProofToken() && tonService.wallet) {
            initializeExistingConnection();
        } else if (tonService.wallet) {
            handleConnectionFailure(t('wallet.invalidTokenDisconnecting'));
        }
    };

    const handleWalletStatusChange = async (wallet) => {
        console.log("Wallet status changed:", wallet);
        
        if (!wallet) {
            handleDisconnection();
            return;
        }

        // Update wallet address immediately
        walletAddress.value = toUserFriendlyAddress(wallet?.account?.address) || null;
        
        // Only proceed with verification if we have tonProof
        if (!wallet?.connectItems?.tonProof) {
            if (!tokenService.hasValidProofToken()) {
                handleConnectionFailure(t('wallet.invalidTokenDetected'));
            }
            return;
        }
        
        // Handle proof verification
        try {
            connectionStatus.value = 'connecting';
            

            const rawInitData = telegramService.getRawInitData();

            console.log(rawInitData);
            
            const verificationResult = await tonProofService.verifyTonProof(
                wallet.connectItems.tonProof.proof,
                wallet.account,
                rawInitData
            );

            if (verificationResult?.ok) {
                notificationStore.addNotification({
                    type: 'success',
                    title: t('wallet.connected'),
                    message: t('wallet.successfullyConnected'),
                    duration: 3000
                });
                connectionStatus.value = 'connected';

                // Fetch user data once wallet is connected
                await userStore.fetchUserData();
                await taskStore.fetchTasks();
                
                // Process referral if needed after successful connection
                await processReferralIfNeeded();
            } else {
                handleConnectionFailure(t('wallet.failedToVerifyProof'));
            }
        } catch (error) {
            handleError(error, 'connection');
        }
    };

    const processReferralIfNeeded = async () => {
        // Skip if already processed, no wallet address or no referral code
        if (referralProcessed.value || !walletAddress.value || !telegramStore.referralCode) {
            return;
        }
        
        try {
            console.log(`Processing referral ${telegramStore.referralCode}`);
            const response = await referralService.bindReferralCode(telegramStore.referralCode);
            
            if (response) {
                notificationStore.addNotification({
                    type: 'success',
                    title: t('referral.applied'),
                    message: t('referral.codeAppliedSuccess'),
                    duration: 3000
                });
                
                // Mark as processed to prevent duplicate processing
                referralProcessed.value = true;
                
                // Clear the referral code
                telegramStore.clearReferralCode();
            } else {
                console.error(t('referral.bindingFailedNoResponse'));
            }
        } catch (error) {
            console.error(t('referral.processingError'), error);
        }
    };

    const initializeExistingConnection = async () => {
        console.log(t('wallet.validTokenFound'));
        walletAddress.value = toUserFriendlyAddress(tonService.wallet?.account?.address)  || null;
        connectionStatus.value = 'connected';
        
        notificationStore.addNotification({
            type: 'success',
            title: t('wallet.reconnected'),
            message: t('wallet.successfullyReconnected'),
            duration: 3000
        });

         // Fetch user data once wallet is connected
         await userStore.fetchUserData();
         await taskStore.fetchTasks();
        
        // Process referral if needed after reconnection
        await processReferralIfNeeded();
    };

    const connectWallet = async () => {
        try {
            connectionStatus.value = 'connecting';
            
            const tonProofPayload = await tonProofService.generateTonProofPayload();
            if (!tonProofPayload) throw new Error(t('wallet.failedToGeneratePayload'));

            console.log(t('wallet.settingConnectParameters'), tonProofPayload);
            tonService.setConnectRequestParameters({ 
                state: "ready", 
                value: { tonProof: tonProofPayload } 
            });

            await tonService.openModal();
            return true;
        } catch (error) {
            return handleError(error, 'connection');
        }
    };

    const disconnectWallet = async () => {
        try {
            await tonService.disconnect();
        } catch (error) {
            handleError(error, 'disconnection', 'warning');
        } finally {
            handleDisconnection();
            userStore.logout();  // Logout when disconnecting the wallet
            await taskStore.fetchTasks();
        }
    };

    const handleError = (error, context, notificationType = 'error') => {
        const contextTranslation = context === 'connection' ? t('wallet.connection') : t('wallet.disconnection');
        const defaultErrorMessage = t('wallet.failedDuring', { context: contextTranslation });
        const message = error?.message || defaultErrorMessage;
        
        console.error(`${t('wallet.errorIn')} ${contextTranslation}:`, error);
        
        notificationStore.addNotification({
            type: notificationType,
            title: context === 'connection' ? t('wallet.connectionFailed') : t('wallet.walletError'),
            message,
            duration: 4000
        });
        
        if (context === 'connection') {
            connectionStatus.value = 'failed';
            disconnectWallet();
        }
        
        lastError.value = { context, message, timestamp: Date.now() };
        return false;
    };

    const handleConnectionFailure = async (message) => {
        notificationStore.addNotification({
            type: 'error',
            title: t('wallet.connectionFailed'),
            message,
            duration: 4000
        });
        connectionStatus.value = 'failed';

        // Logout the user when connection fails
        userStore.logout();  // Add logout here
        await taskStore.fetchTasks();
        await disconnectWallet();
    };

    const handleDisconnection = () => {
        walletAddress.value = null;
        connectionStatus.value = 'disconnected';
        referralProcessed.value = false; // Reset referral processed flag on disconnect
        tokenService.clearProofToken();
    };

    // Reset referral processed flag when referral code changes
    watch(() => telegramStore.referralCode, () => {
        referralProcessed.value = false;
    });

    // Watch for connection status changes (for debugging)
    if (import.meta.env.DEV) {
        watch(connectionStatus, (newStatus, oldStatus) => {
            console.log(t('wallet.statusChanged', { oldStatus, newStatus }));
        });
    }

    return {
        // State
        walletAddress,
        connectionStatus,
        lastError,
        
        // Computed
        formattedAddress,
        isConnected,
        
        // Methods
        init,
        connectWallet,
        disconnectWallet,
        processReferralIfNeeded
    };
});