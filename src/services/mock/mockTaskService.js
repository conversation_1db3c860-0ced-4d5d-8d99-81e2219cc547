/**
 * Mock Task Service
 * 
 * This is a mock version of the task service that returns
 * predetermined tasks in various uncompleted states.
 */

// Mock data for testing all three states of tasks
const mockTasks = [
  {
    id: 1,
    name: "Daily Reward +1 Chest",
    type: "DAILY_SIGNIN",
    repeatInterval: "day",
    isCompleted: false,
    canComplete: true,  // Available to complete
    lastCompleteTime: null
  },
  {
    id: 2,
    name: "Join our Telegram Channel +1 Chest",
    type: "JOIN_TELEGRAM",
    repeatInterval: "once",
    isCompleted: false,
    canComplete: false, // Not available to complete
    lastCompleteTime: null
  },
  {
    id: 3,
    name: "Join our Twitter Channel +1 Chest",
    type: "JOIN_TWITTER",
    repeatInterval: "once",
    isCompleted: false,
    canComplete: true,  // Available to complete
    lastCompleteTime: null
  },
  {
    id: 4,
    name: "Follow us on Instagram +1 Chest",
    type: "FOLLOW_INSTAGRAM",
    repeatInterval: "once",
    isCompleted: false,
    canComplete: false, // Not available to complete
    lastCompleteTime: null
  },
  {
    id: 5,
    name: "Vote on Discord +1 Chest",
    type: "VOTE_DISCORD",
    repeatInterval: "day",
    isCompleted: false,
    canComplete: true,  // Available to complete
    lastCompleteTime: null
  }
];

// Generate random chest rewards to simulate the API
const generateChestReward = () => {
// 1 in 10 chance of getting a jackpot
const hasJackpot = Math.random() < 0.1;

return {
  openedCount: 1,
  chestIds: [Math.floor(Math.random() * 100) + 1],
  rewards: [
    {
      level: Math.floor(Math.random() * 4) + 1,
      items: [
        {
          type: "fragment_green",
          amount: Math.floor(Math.random() * 30) + 5
        },
        {
          type: "gem",
          amount: Math.floor(Math.random() * 5000) + 1000
        }
      ]
    }
  ],
  summary: {
    ticket: Math.floor(Math.random() * 2),
    fragment_green: Math.floor(Math.random() * 30) + 5,
    fragment_blue: Math.floor(Math.random() * 10),
    fragment_purple: Math.floor(Math.random() * 5),
    fragment_gold: Math.random() < 0.2 ? 1 : 0,
    ton: Math.random() < 0.1 ? Math.floor(Math.random() * 5) + 1 : 0,
    gem: Math.floor(Math.random() * 5000) + 1000
  },
  levelSummary: {
    level1: 1,
    level2: 0,
    level3: 0,
    level4: 0
  },
  shareLinks: [],
  jackpotWinner: hasJackpot ? {
    level: Math.floor(Math.random() * 4) + 1,
    amount: Math.floor(Math.random() * 50) + 10,
    userId: 1,
    walletId: 1,
    poolId: 1,
    winTime: new Date().toISOString()
  } : null
};
};

export default {
/**
 * Gets the mocked list of available tasks
 * 
 * @returns {Promise<Array>} Array of mock tasks
 */
async getTaskList() {
  // Simulate API delay
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log('Mock tasks:', mockTasks);
      resolve(mockTasks);
    }, 300);
  });
},

/**
 * Simulates completing a task and claims the chest reward
 * 
 * @param {number} taskId - The ID of the task to complete
 * @returns {Promise<Object>} Mock API response
 */
async completeTask(taskId) {
  // Simulate API delay
  return new Promise((resolve) => {
    setTimeout(() => {
      // Find the task
      const task = mockTasks.find(t => t.id === taskId);
      
      if (!task) {
        resolve({
          ok: false,
          message: 'Task not found'
        });
        return;
      }
      
      if (!task.canComplete) {
        resolve({
          ok: false,
          message: 'Task is not available for completion'
        });
        return;
      }
      
      // Mark as completed in the mock data
      task.isCompleted = true;
      task.canComplete = false;
      task.lastCompleteTime = new Date().toISOString();
      
      // Generate chest reward data that matches the API format
      const chestReward = generateChestReward();
      
      resolve({
        ok: true,
        data: {
          message: 'Task completed successfully',
          task: task,
          chestReward: chestReward
        },
        message: 'Task completed successfully'
      });
    }, 300);
  });
}
};