// mockReferralService.js
const mockReferralService = {
  // Flag to control the mock behavior
  hasMockChests: true,
  mockChestCount: 3,
  dailyChestAvailable: true,
  
  // Mock implementation of claimDailyChests
  async claimDailyChests() {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 800));
    
    if (!this.dailyChestAvailable) {
      return {
        ok: false,
        message: "You have already claimed your daily chest today"
      };
    }
    
    // Set daily chest as claimed
    this.dailyChestAvailable = false;
    
    // Return mock daily chest reward
    return {
      dailyChests: 1,
      rewards: {
        openedCount: 1,
        chestIds: [Math.floor(Math.random() * 100) + 1],
        rewards: [
          {
            level: 2,
            items: [
              {
                type: "fragment_green",
                amount: Math.floor(Math.random() * 20) + 5
              },
              {
                type: "fragment_blue",
                amount: Math.floor(Math.random() * 10) + 1
              },
              {
                type: "gem",
                amount: Math.floor(Math.random() * 5000) + 1000
              }
            ]
          }
        ],
        summary: {
          ticket: 0,
          fragment_green: Math.floor(Math.random() * 20) + 5,
          fragment_blue: Math.floor(Math.random() * 10) + 1,
          fragment_purple: 0,
          fragment_gold: 0,
          ton: 0,
          gem: Math.floor(Math.random() * 5000) + 1000
        },
        levelSummary: {
          level1: 0,
          level2: 1,
          level3: 0,
          level4: 0
        },
        shareLinks: [],
        jackpotWinner: Math.random() < 0.1 ? {
          level: 1,
          amount: Math.floor(Math.random() * 50) + 10,
          userId: 1,
          walletId: 1,
          poolId: 1,
          winTime: new Date().toISOString()
        } : null
      }
    };
  },
  
  // Reset daily chest availability (for testing purposes)
  resetDailyChest() {
    this.dailyChestAvailable = true;
    console.log("Daily chest is now available again");
  },
  
  // Mock implementation of getReferralChest
  async getReferralChest() {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    if (this.hasMockChests) {
      return {
        count: {
          total: this.mockChestCount,
          premium_referral: 1,
          normal_referral: 2
        },
        limit: 10
      };
    } else {
      return {
        count: {
          total: 0,
          premium_referral: 0,
          normal_referral: 0
        },
        limit: 10
      };
    }
  },
  
  // Mock implementation of openReferralChests
  async openReferralChests() {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    if (this.hasMockChests) {
      return {
        openedCount: this.mockChestCount,
        chestIds: [1, 2, 3].slice(0, this.mockChestCount),
        rewards: [
          {
            level: 1,
            items: [{ type: "gem", amount: 50 }]
          },
          {
            level: 1,
            items: [{ type: "ticket", amount: 1 }]
          },
          {
            level: 2,
            items: [{ type: "gem", amount: 100 }]
          }
        ].slice(0, this.mockChestCount),
        summary: {
          ticket: 1,
          ticket_fragment: 0,
          ton: 0,
          gem: 150
        },
        levelSummary: {
          level1: 2,
          level2: 1,
          level3: 0,
          level4: 0
        },
        shareLinks: []
      };
    } else {
      // This would be an empty response if no chests
      return null;
    }
  },
    // Mock implementation of getDownlineList
  async getDownlineList(level = 1, page = 1, pageSize = 20) {
    return mockGetDownlineList(level, page, pageSize);
  },
  
  // Toggle the mock behavior
  toggleChests() {
    this.hasMockChests = !this.hasMockChests;
    console.log(`Mock chests are now ${this.hasMockChests ? 'available' : 'unavailable'}`);
  },
  
  // Set a specific number of chests
  setChestCount(count) {
    this.mockChestCount = count;
    this.hasMockChests = count > 0;
    console.log(`Mock chest count set to ${count}`);
  }
};

// Generate random wallet address
const generateWalletAddress = () => {
const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
let result = 'UQ';
for (let i = 0; i < 40; i++) {
  result += chars.charAt(Math.floor(Math.random() * chars.length));
}
return result;
};

// Generate a random date within the last month
const generateRecentDate = () => {
const now = new Date();
const pastDate = new Date(now.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000);
return pastDate.toISOString().split('T')[0].replace(/-/g, '-') + ' ' + 
       ('0' + pastDate.getHours()).slice(-2) + ':' + 
       ('0' + pastDate.getMinutes()).slice(-2) + ':' + 
       ('0' + pastDate.getSeconds()).slice(-2);
};

// Generate mock users
const generateMockUsers = (count, level) => {
const users = [];
const usernames = ['Crypto_Wolf', 'BlockchainMaster', 'TONExplorer', 'DiamondHands', 
                   'MoonShotFinder', 'SatoshiDreamer', 'AirdropHunter', 'TokenWhale', 
                   'MetaversePlayer', 'NFTCollector', 'DeFiKing', 'ChainGamer'];

for (let i = 0; i < count; i++) {
  const userId = 1000 + i;
  const user = {
    userId: userId,
    username: usernames[Math.floor(Math.random() * usernames.length)] + '_' + Math.floor(Math.random() * 1000),
    photoUrl: "",
    registerTime: generateRecentDate(),
    walletId: userId,
    walletAddress: generateWalletAddress(),
    gameCount: Math.floor(Math.random() * 50),
    totalBetAmount: Math.floor(Math.random() * 10000),
    todayChestCount: Math.floor(Math.random() * 3),
    weekChestCount: Math.floor(Math.random() * 10),
    todayBoostMinutes: String(Math.floor(Math.random() * 60)),
    weekBoostMinutes: String(Math.floor(Math.random() * 300))
  };
  
  // Add parent info for level 2 downlines
  if (level === 2) {
    user.parentId = 900 + Math.floor(Math.random() * 10);
    user.parentUsername = 'Parent_' + user.parentId;
  }
  
  users.push(user);
}

return users;
};

// Mock implementation of getDownlineList
const mockGetDownlineList = async (level = 1, page = 1, pageSize = 20) => {
// Simulate API delay
await new Promise(resolve => setTimeout(resolve, 800));

// Generate total number of downline users
const totalUsers = level === 1 ? 35 : 18;

// Calculate the slice of users to return based on pagination
const start = (page - 1) * pageSize;
const end = Math.min(start + pageSize, totalUsers);
const actualCount = Math.min(pageSize, end - start);

// Generate the mock users for this page
const users = generateMockUsers(actualCount, level);

// Return the mock API response
return {
  users: users,
  total: totalUsers,
  page: page,
  pageSize: pageSize,
  level: level
};
};

export default mockReferralService;