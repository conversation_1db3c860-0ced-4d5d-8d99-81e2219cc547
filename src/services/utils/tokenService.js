/**
 * Enhanced Token Service
 * 
 * This service handles token management for authentication with Telegram integration,
 * including storing, retrieving, validating, and clearing tokens based on Telegram ID.
 */

import TelegramService from './telegramService'; // Import the Telegram service

export default {
    /**
     * Retrieves the authentication token from storage for the current Telegram user
     * @returns {string|null} The token or null if not found
     */
    getToken() {
        const telegramId = this.getTelegramId();
        if (!telegramId) {
            return null;
        }
        return localStorage.getItem(`tonProofToken_${telegramId}`);
    },
    
    /**
     * Gets the token expiry timestamp for the current Telegram user
     * @returns {number} Expiry timestamp in milliseconds or 0 if not found
     */
    getTokenExpiry() {
        const telegramId = this.getTelegramId();
        if (!telegramId) {
            return 0;
        }
        return parseInt(localStorage.getItem(`tonProofTokenExpiry_${telegramId}`)) || 0;
    },
    
    /**
     * Stores a token for the current Telegram user
     * @param {string} token - The token to store
     * @param {number} expiryTime - Expiry timestamp in milliseconds
     * @returns {boolean} True if successful, false if Telegram not initialized
     */
    storeToken(token, expiryTime) {
        const telegramId = this.getTelegramId();
        if (!telegramId) {
            console.error('Cannot store token: Telegram ID not available');
            return false;
        }
        
        localStorage.setItem(`tonProofToken_${telegramId}`, token);
        localStorage.setItem(`tonProofTokenExpiry_${telegramId}`, expiryTime.toString());
        return true;
    },
    
    /**
     * Checks if a valid token exists and is not expired for the current Telegram user
     * @returns {boolean} True if token exists and is valid, false otherwise
     */
    hasValidProofToken() {
        const token = this.getToken();
        const expiry = this.getTokenExpiry();
        return token && Date.now() < expiry;
    },
    
    /**
     * Clears the token and expiry from storage for the current Telegram user
     * @returns {boolean} True if successful, false if Telegram not initialized
     */
    clearProofToken() {
        const telegramId = this.getTelegramId();
        if (!telegramId) {
            console.error('Cannot clear token: Telegram ID not available');
            return false;
        }
        
        localStorage.removeItem(`tonProofToken_${telegramId}`);
        localStorage.removeItem(`tonProofTokenExpiry_${telegramId}`);
        return true;
    },
    
    /**
     * Gets the current Telegram user ID
     * @returns {string|null} The Telegram user ID or null if not available
     */
    getTelegramId() {
        if (!TelegramService.isInitialized()) {
            console.warn('Telegram service not initialized');
            return null;
        }
        
        const authData = TelegramService.getAuthData();
        if (!authData || !authData.user || !authData.user.id) {
            console.warn('Telegram user data not available');
            return null;
        }
        
        return authData.user.id.toString();
    },
    
    /**
     * Verifies if the token belongs to the current Telegram user
     * @param {string} token - Token to verify
     * @returns {boolean} True if token is valid for current user, false otherwise
     */
    verifyTokenOwnership(token) {
        const currentToken = this.getToken();
        return token === currentToken && this.hasValidProofToken();
    }
}