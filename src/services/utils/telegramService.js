/**
 * Telegram Service
 * 
 * This service handles Telegram Web App interactions, including
 * initialization, parsing init data, and UI controls like the back button.
 */

// Internal state
let authData = null;
let rawInitData = null;
let telegramInitialized = false;

/**
 * Initializes the Telegram Web App
 * @returns {Promise<Object|null>} Parsed initialization data or rejects with error
 */
function initTelegram() {
    return new Promise((resolve, reject) => {
        if (typeof window.Telegram !== 'undefined' && window.Telegram.WebApp) {
            rawInitData = window.Telegram?.WebApp?.initData || import.meta.env.VITE_TELEGRAM_INIT_DATA_FALLBACK;

            // Use it however you like:
            console.log("rawInitData :" + rawInitData);
            

            if (rawInitData) {
                authData = parseInitData(rawInitData);
                telegramInitialized = true;

                // ✅ Tell Telegram the app is ready and request full-screen
                window.Telegram.WebApp.ready();
                window.Telegram.WebApp.expand();

                console.log("Telegram initialized successfully:", authData);
                resolve(authData);
            } else {
                reject(new Error("Telegram initData is empty"));
            }
        } else {
            console.warn("Telegram SDK not ready or initData missing");
            reject(new Error("Telegram SDK not ready or initData missing"));
        }
    });
}

/**
 * Parses the URL-encoded init data from Telegram
 * @param {string} initDataString - Raw initialization data string
 * @returns {Object|null} Parsed data object or null on error
 */
function parseInitData(initDataString) {
    try {
        // Decode the URL-encoded string
        const decoded = decodeURIComponent(initDataString);
        
        // Split into key-value pairs
        const pairs = decoded.split('&');
        const result = {};
        
        pairs.forEach(pair => {
            const [key, value] = pair.split('=');
            if (key === 'user') {
                // Parse the user JSON string
                result[key] = JSON.parse(decodeURIComponent(value));
            } else {
                result[key] = value;
            }
        });
        
        return result;
    } catch (error) {
        console.error('Error parsing Telegram init data:', error);
        return null;
    }
}

/**
 * Sets up the back button functionality
 */
function setupBackButton() {
    const backButton = window.Telegram.WebApp.BackButton;

    if (!backButton) return;

    backButton.show();
    backButton.onClick(function () {
        // Back button handler - currently empty
    });
}

/**
 * Checks if Telegram WebApp is available
 * @returns {boolean} True if Telegram WebApp is available
 */
function isTelegramReady() {
    return typeof window.Telegram !== 'undefined' && window.Telegram.WebApp;
}

/**
 * Gets the current authentication data
 * @returns {Object|null} The parsed auth data or null if not initialized
 */
function getAuthData() {
    return authData;
}

/**
 * Gets the current authentication data
 * @returns {Object|null} The parsed auth data or null if not initialized
 */
function getRawInitData() {
    return rawInitData;
}


/**
 * Checks if Telegram has been successfully initialized
 * @returns {boolean} True if initialized
 */
function isInitialized() {
    return telegramInitialized;
}

// Export the service as default
export default {
    initTelegram,
    parseInitData,
    setupBackButton,
    isTelegramReady,
    getAuthData,
    getRawInitData,
    isInitialized
};