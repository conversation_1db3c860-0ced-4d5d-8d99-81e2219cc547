/**
 * TON Connect Service
 * 
 * This service creates a TonConnectUI instance for connecting to TON blockchain.
 * It handles wallet connections and interactions with the TON network.
 */

import { TonConnectUI } from "@tonconnect/ui";


// Initialize TonConnectUI with the manifest URL
const tonService = new TonConnectUI({
    manifestUrl: "https://raw.githubusercontent.com/TextGuySemicolon/moofun-project/refs/heads/main/public/tonconnect-manifest-v2.json"
});

export default tonService;