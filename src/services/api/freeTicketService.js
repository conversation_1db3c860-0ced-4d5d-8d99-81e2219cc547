import apiClient from '../apiClient';
import { useUserStore } from '@/stores/userStore';
import { tokenService } from '@/services';

export default {
  /**
   * Transfers free tickets to a specified wallet address
   * 
   * @param {string} toWalletAddress - Receiver's wallet address
   * @param {number} amount - Number of tickets to transfer
   * @returns {Promise} Object containing transfer result or null on error
   */
  async transferFreeTickets(toWalletAddress, amount) {
    try {
      // Check if we have a valid token
      if (!tokenService.hasValidProofToken()) {
        console.error('Valid authorization token not found');
        return null;
      }
      
      // Get the token
      const token = tokenService.getToken();
      
      // Set the authorization header
      apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      apiClient.defaults.headers.common['Accept-Language'] = localStorage.getItem('app_language') || 'en';
      
      // Prepare request body
      const requestBody = {
        toWalletAddress,
        amount
      };
      
      // Make the API call
      const response = await apiClient.post('/free-ticket/transfer', requestBody);
      console.log(response);


      
      // Return the response data if request was successful
      if (response.data) {
        // Fetch user data after transfering ticket
        const userStore = useUserStore();
        await userStore.fetchUserData();

        return response.data || null;
      }
      
      return null;
    } catch (error) {
      console.error('Error transferring free tickets:', error);
      return error.response.data;
    }
  },

  /**
   * Gets the remaining daily transfer limit for free tickets
   * 
   * @returns {Promise} Object containing the remaining limit information or null on error
   */
  async getRemainingTransferLimit() {
    try {
      // Check if we have a valid token
      if (!tokenService.hasValidProofToken()) {
        console.error('Valid authorization token not found');
        return null;
      }
      
      // Get the token
      const token = tokenService.getToken();
      
      // Set the authorization header
      apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      apiClient.defaults.headers.common['Accept-Language'] = localStorage.getItem('app_language') || 'en';
      
      // Make the API call
      const response = await apiClient.get('/free-ticket/remaining-limit');
      console.log(response);
      
      // Return the response data if request was successful
      if (response.data && response.data.data) {
        return response.data.data.remainingLimit || null;
      }
      
      return null;
    } catch (error) {
      console.error('Error getting remaining transfer limit:', error);
      return null;
    }
  }
}