import apiClient from '../apiClient';
import { tokenService } from '@/services';

export default {

  /**
   * Crafts tickets using specific fragment types
   * 
   * @param {string} fragmentType - Type of fragment to use ('fragment_green', 'fragment_blue', 'fragment_purple', 'fragment_gold')
   * @param {number} quantity - Number of crafting operations to perform
   * @returns {Promise} Object containing updated balance information or null on error
   */
  async craftTicketsWithFragments(fragmentType, quantity) {
    try {
      // Check if we have a valid token
      if (!tokenService.hasValidProofToken()) {
        console.error('Valid authorization token not found');
        return null;
      }
      
      // Get the token
      const token = tokenService.getToken();
      
      // Set the authorization header
      apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      apiClient.defaults.headers.common['Accept-Language'] = localStorage.getItem('app_language') || 'en';
      
      // Prepare request body
      const requestBody = {
        fragmentType,
        quantity
      };
      
      // Make the API call
      const response = await apiClient.post('/fragment/craft-ticket', requestBody);
      console.log(response);
      
      // Return the updated balance data if request was successful
      if (response.data) {
        return response.data || null;
      }
      
      return null;
    } catch (error) {
      console.error('Error crafting tickets with fragments:', error);
      return null;
    }
  }
}