/**
 * Ticket Service
 * 
 * This service handles operations related to tickets
 * including purchasing tickets.
 */

import apiClient from '../apiClient';
import { tokenService } from '@/services';

export default {
  /**
   * Purchases tickets
   * 
   * @param {number} quantity - The number of tickets to purchase
   * @returns {Promise<Object|null>} The API response or null on error
   */
  async purchaseTickets(quantity) {
    try {
      // Validate quantity
      if (!Number.isInteger(quantity) || quantity <= 0) {
        console.error('Invalid quantity. Must be a positive integer.');
        return null;
      }
      
      // Check if we have a valid token
      if (!tokenService.hasValidProofToken()) {
        console.error('Valid authorization token not found');
        return null;
      }
      
      // Get the token
      const token = tokenService.getToken();
      
      // Set the authorization header
      apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      
      // Make the API call
      const response = await apiClient.post('/ticket/purchase', {
        quantity: quantity
      });
      
      return response.data;
    } catch (error) {
      console.error('Error purchasing tickets:', error);
      return null;
    }
  }
};