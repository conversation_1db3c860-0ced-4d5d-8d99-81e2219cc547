/**
 * Wallet Service
 * 
 * This service handles operations related to the user's wallet
 * including fetching transaction history.
 */

import apiClient from '../apiClient';
import { tokenService } from '@/services';

export default {
  /**
   * Gets the wallet transaction history with pagination
   * 
   * @param {Object} options - Pagination options
   * @param {number} [options.page=1] - The page number to fetch
   * @param {number} [options.limit=10] - Number of records per page
   * @returns {Promise<Object|null>} Paginated transaction history or null on error
   */
  async getWalletHistory(options = {}) {
    try {
      // Set default pagination values
      const page = options.page || 1;
      const limit = options.limit || 10;
      
      // Validate pagination parameters
      if (!Number.isInteger(page) || page < 1 || !Number.isInteger(limit) || limit < 1) {
        console.error('Invalid pagination parameters. Page and limit must be positive integers.');
        return null;
      }
      
      // Check if we have a valid token
      if (!tokenService.hasValidProofToken()) {
        console.error('Valid authorization token not found');
        return null;
      }
      
      // Get the token
      const token = tokenService.getToken();
      
      // Set the authorization header
      apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      
      // Prepare query parameters
      const params = {
        page,
        limit
      };
      
      // Make the API call
      const response = await apiClient.get('https://wolf.jpegonapechain.com/api/wallet/history', { params });
      
      // Return the transaction history if request was successful
      if (response.data && response.data.ok) {
        return {
          transactions: response.data.transactions || [],
          pagination: response.data.pagination || {
            total: 0,
            page: page,
            limit: limit,
            pages: 0
          }
        };
      }
      
      return null;
    } catch (error) {
      console.error('Error fetching wallet history:', error);
      return null;
    }
  }
};