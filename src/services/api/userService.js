/**
 * User Service
 * 
 * This service handles operations related to user information
 * including fetching the current user's profile data.
 */

import apiClient from '../apiClient';
import { tokenService } from '@/services';

export default {
  /**
   * Gets the current user's information
   * 
   * @returns {Promise<Object|null>} User information object or null on error
   */
  async getCurrentUser() {
    try {
      // Check if we have a valid token
      if (!tokenService.hasValidProofToken()) {
        console.error('Valid authorization token not found');
        return null;
      }
      
      // Get the token
      const token = tokenService.getToken();
      
      // Set the authorization header
      apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      
      // Make the API call
      const response = await apiClient.get('/user/me');
      console.log(response);

      // Return the user data if request was successful
      if (response.data && response.data.ok) {
        return response.data.data || null;
      }
      
      return null;
    } catch (error) {
      console.error('Error fetching user information:', error);
      return null;
    }
  }
};