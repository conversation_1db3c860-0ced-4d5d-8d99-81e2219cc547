/**
 * TON Proof Authentication Service
 * 
 * This service handles TON blockchain authentication using the TON Connect protocol.
 * It provides methods to generate authentication payloads and verify signed proofs.
 */

import apiClient from '../apiClient';
import { tokenService } from '@/services';

export default {
  /**
   * Generates a payload for TON Proof authentication
   * 
   * @returns {Promise<string|null>} The payload string if successful, null otherwise
   */
  async generateTonProofPayload() {
    try {
      const response = await apiClient.post('/ton-proof/generate-payload');
      
      // Return payload if the response was successful, otherwise null
      return response.data.ok ? response.data.payload : null;
    } catch (error) {
      console.error('Error fetching TON Proof payload:', error);
      return null;
    }
  },

  /**
   * Verifies a signed TON Proof and stores the authentication token if successful
   * 
   * @param {Object} proof - The proof object from TON Connect
   * @param {string} proof.domain - Domain that requested the proof
   * @param {number} proof.timestamp - Timestamp when the proof was created
   * @param {string} proof.payload - The payload that was signed
   * @param {string} proof.signature - The signature created by the wallet
   * 
   * @param {Object} account - The wallet account information
   * @param {string} account.address - Wallet address
   * @param {string} account.chain - Network identifier (e.g., 'mainnet')
   * @param {string} account.publicKey - Public key of the wallet
   * @param {string} account.walletStateInit - Wallet state initialization data
   * 
   * @param {string} initData - Additional initialization data
   * 
   * @returns {Promise<Object|null>} The API response object or null on error
   */
  async verifyTonProof(proof, account, initData) {
    try {
      // Prepare the request data with proper type conversion
      const requestData = {
        address: String(account.address),
        network: String(account.chain),
        proof: {
          domain: proof.domain,
          timestamp: proof.timestamp,
          payload: proof.payload,
          signature: proof.signature,
          state_init: account.walletStateInit
        },
        public_key: String(account.publicKey),
        initData: String(initData)
      };

      // Send verification request to the API
      const response = await apiClient.post('/ton-proof/check-proof', requestData);
      console.log(response);

      // If verification was successful and a token was received
      if (response.data.ok && response.data.token) {
        // Calculate expiry time (30 days from now)
        const expiryTime = Date.now() + 30 * 24 * 60 * 60 * 1000;
        
        // Use the enhanced token service instead of direct localStorage access
        tokenService.storeToken(response.data.token, expiryTime);
        
        return response.data;
      }

      return response.data;
    } catch (error) {
      console.error('Error verifying TON Proof:', error);
      return null;
    }
  }
};