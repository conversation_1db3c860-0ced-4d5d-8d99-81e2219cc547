/**
 * Referral Service
 * 
 * This service handles operations related to referral codes
 * including binding referral codes and managing referral relationships.
 */

import apiClient from '../apiClient';
import { tokenService } from '@/services';

export default {
  /**
   * Binds a referral code to the current user
   * 
   * @param {string} referralCode - The referral code to bind
   * @returns {Promise<Object|null>} The API response or null on error
   */
  async bindReferralCode(referralCode) {
    try {
      // Validate referral code
      if (!referralCode || typeof referralCode !== 'string') {
        console.error('Invalid referral code provided');
        return null;
      }
      
      // Check if we have a valid token
      if (!tokenService.hasValidProofToken()) {
        console.error('Valid authorization token not found');
        return null;
      }
      
      // Get the token
      const token = tokenService.getToken();
      
      // Set the authorization header
      apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      apiClient.defaults.headers.common['Accept-Language'] = localStorage.getItem('app_language') || 'en';
      
      // Make the API call
      const response = await apiClient.post('/referral/bind', {
        referralCode: referralCode
      });
      
      console.log('Referral binding response:', response);
      
      // Return the response data if request was successful
      if (response.data) {
        return response.data;
      }
      
      return null;
    } catch (error) {
      console.error('Error binding referral code:', error);
      return null;
    }
  },

  /**
   * Gets the current user's referral information
   * 
   * @returns {Promise<Object|null>} Referral information or null on error
   */
  async getReferralInfo() {
    try {
      // Check if we have a valid token
      if (!tokenService.hasValidProofToken()) {
        console.error('Valid authorization token not found');
        return null;
      }
      
      // Get the token
      const token = tokenService.getToken();
      
      // Set the authorization header
      apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      apiClient.defaults.headers.common['Accept-Language'] = localStorage.getItem('app_language') || 'en';
      
      // Make the API call
      const response = await apiClient.get('/referral/info');
      
      console.log('Referral info response:', response);
      
      // Return the referral data if request was successful
      if (response.data && response.data.ok) {
        return response.data.data || null;
      }
      
      return null;
    } catch (error) {
      console.error('Error fetching referral info:', error);
      return null;
    }
  },

  /**
   * Gets the list of users referred by the current user
   * 
   * @param {number} page - Page number for pagination (default: 1)
   * @param {number} limit - Number of items per page (default: 20)
   * @returns {Promise<Object|null>} Referred users list or null on error
   */
  async getReferredUsers(page = 1, limit = 20) {
    try {
      // Check if we have a valid token
      if (!tokenService.hasValidProofToken()) {
        console.error('Valid authorization token not found');
        return null;
      }
      
      // Get the token
      const token = tokenService.getToken();
      
      // Set the authorization header
      apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      apiClient.defaults.headers.common['Accept-Language'] = localStorage.getItem('app_language') || 'en';
      
      // Prepare query parameters
      const params = { page, limit };
      
      // Make the API call
      const response = await apiClient.get('/referral/referred-users', { params });
      
      console.log('Referred users response:', response);
      
      // Return the users data if request was successful
      if (response.data && response.data.ok) {
        return {
          users: response.data.data || [],
          pagination: response.data.pagination || {
            total: 0,
            page: page,
            limit: limit,
            pages: 0
          }
        };
      }
      
      return null;
    } catch (error) {
      console.error('Error fetching referred users:', error);
      return null;
    }
  }
};
