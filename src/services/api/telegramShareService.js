import apiClient from '../apiClient';
import { tokenService } from '@/services';

export default {
  /**
   * Shares a boost via the Telegram WebApp and provides rewards to the link creator.
   * 
   * @param {string} initData - The Telegram WebApp initData for user authentication.
   * @param {string} code - The unique share link code.
   * @returns {Promise<Object|null>} Object containing the boost time and gem rewards, or null on error.
   */
  async shareBoost(initData, code) {
    try {
      // Get the token
      const token = tokenService.getToken();
      
      // Set the authorization header
      apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      apiClient.defaults.headers.common['Accept-Language'] = localStorage.getItem('app_language') || 'en';
      
      // Make the API call to share the boost
      const response = await apiClient.post('/telegram-share/boost', {
        initData: initData,
        code: code
      });

      console.log("telegram-share/boost Response:", response);

      if (response.data?.ok) {
        return {
          success: true,
          data: response.data.data || null
        };
      }
  
      return {
        success: false,
        message: response.data?.message || 'Unknown error'
      };
    } catch (error) {
      console.error('Error sharing boost:', error);

      let message = 'Something went wrong';
  
      if (error.response?.data?.message) {
        message = error.response.data.message;
      } else if (error.message) {
        message = error.message;
      }
  
      return {
        success: false,
        message
      };
    }
  }
};
