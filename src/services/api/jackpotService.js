
import apiClient from '../apiClient';
import { tokenService } from '@/services';

export default {

    /**
 * Gets the user's jackpot chest countdown status
 * 
 * @returns {Promise<Object|null>} Object containing countdown status or null on error
 */
async getJackpotChestCountdown() {
    try {
      // Check if we have a valid token
      if (!tokenService.hasValidProofToken()) {
        console.error('Valid authorization token not found');
        return null;
      }
      
      // Get the token
      const token = tokenService.getToken();
      
      // Set the authorization header
      apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      
      // Make the API call
      const response = await apiClient.get('/jackpot-chest/countdown');
      console.log(response);
      
      // Return the countdown data if request was successful
      if (response.data && response.data.ok) {
        return response.data.data || null;
      }
      
      return null;
    } catch (error) {
      console.error('Error fetching jackpot chest countdown:', error);
      return null;
    }
  },
  /**
 * Collects and opens the user's countdown jackpot chest
 * 
 * @returns {Promise<Object|null>} Object containing collected chest and rewards or null on error
 */
async collectJackpotChest() {
    try {
      // Check if we have a valid token
      if (!tokenService.hasValidProofToken()) {
        console.error('Valid authorization token not found');
        return null;
      }
      
      // Get the token
      const token = tokenService.getToken();
      
      // Set the authorization header
      apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      
      // Make the API call to collect the chest
      const response = await apiClient.post('/jackpot-chest/collect');
      console.log(response);

      // Return the chest data if request was successful
      if (response.data && response.data.ok) {
        return response.data.data || null;
      }
      
      return null;
    } catch (error) {
      console.error('Error collecting jackpot chest:', error);
      return null;
    }
  },
  /**
 * Checks whether the user has already collected the four jackpot chests
 * 
 * @returns {Promise<boolean|null>} True if already collected, false if not, null on error
 */
async getFourJackpotChestsStatus() {
  try {
    // Check for a valid authorization token
    if (!tokenService.hasValidProofToken()) {
      console.error('Valid authorization token not found');
      return null;
    }

    // Get the token
    const token = tokenService.getToken();

    // Set the authorization header
    apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;

    // Make the GET request
    const response = await apiClient.get('/jackpot-chest/four-chests-status');
    console.log('getFourJackpotChestsStatus response:', response);

    // Return the status if successful
    if (response.data && response.data.ok && typeof response.data.data?.hasCollected === 'boolean') {
      return response.data.data.hasCollected;
    } else {
      console.warn('Unexpected response:', response.data);
      return null;
    }
  } catch (error) {
    console.error('Error checking four jackpot chest collection status:', error);
    return null;
  }
}
,
  /**
 * Collects and opens four jackpot chests in one request
 * 
 * @returns {Promise<Object|null>} Object containing the rewards or null on error
 */
async collectFourJackpotChests() {
  try {
    // Check if we have a valid token
    if (!tokenService.hasValidProofToken()) {
      console.error('Valid authorization token not found');
      return null;
    }

    // Get the token
    const token = tokenService.getToken();

    // Set the authorization header
    apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;

    // Make the API call to collect the four chests
    const response = await apiClient.post('/jackpot-chest/collect-four-chests');
    console.log(response);

    // Return the data if successful
    if (response.data && response.data.ok) {
      return response.data.data;
    } else {
      console.warn('Failed to collect four chests:', response.data?.message || 'Unknown error');
      return null;
    }
  } catch (error) {
    console.error('Error collecting four jackpot chests:', error);
    return null;
  }
}

}