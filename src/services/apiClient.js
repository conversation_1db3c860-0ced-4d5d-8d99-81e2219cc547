import axios from 'axios';

const apiClient = axios.create({
    baseURL: import.meta.env.VITE_API_BASE_URL || 'https://wolf.jpegonapechain.com/api',
    headers: { 'Content-Type': 'application/json' }
});

// 添加请求拦截器来处理认证
apiClient.interceptors.request.use(
    function (config) {
        // 从localStorage获取语言设置
        const lang = localStorage.getItem('app_language') || 'en';
        config.headers['Accept-Language'] = lang;

        return config;
    },
    function (error) {
        return Promise.reject(error);
    }
);

// 添加响应拦截器来处理错误
apiClient.interceptors.response.use(
    function (response) {
        return response;
    },
    function (error) {
        console.error('API请求错误:', error);

        // 处理网络错误
        if (!error.response) {
            console.error('网络连接错误或服务器无响应');
        } else {
            console.error('HTTP错误状态:', error.response.status);
            console.error('错误响应:', error.response.data);
        }

        return Promise.reject(error);
    }
);

export default apiClient;
