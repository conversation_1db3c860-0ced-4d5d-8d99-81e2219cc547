export function formatNumber(value, maxDigits = 4) {
    if (value < 1000) return value.toString(); // No conversion needed for small numbers

    const units = ["", "k", "M", "B", "T"];
    let unitIndex = 0;
    
    while (value >= 1000 && unitIndex < units.length - 1) {
        value /= 1000;
        unitIndex++;
    }
    
    // Determine how many decimal places to show (max 2)
    let decimalPlaces = Math.min(2, maxDigits - Math.floor(value).toString().length);
    decimalPlaces = Math.max(1, decimalPlaces); // Ensure at least 1 decimal place
    
    // Format with fixed decimal places
    let formattedValue = value.toFixed(decimalPlaces).replace(/\.0+$/, ""); // Remove trailing zeros
    
    return formattedValue + units[unitIndex];
}