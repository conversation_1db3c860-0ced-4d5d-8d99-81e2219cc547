<script setup>
import TopSection from '@/components/TopSection.vue';
import FourChestPopupOverlay from '@/components/jackpot/FourChestPopupOverlay.vue';
import Background from '@/components/common/Background.vue';
import { GameMenu, gameMenuItems } from '@/features/gameMenu'
import GameNotification from '@/components/vue/GameNotification.vue';
import { OpenChestOverlay } from '@/features/openChest';
import { PlayerPopup } from '@/features/userInfo';

</script>

<!-- MainLayout.vue -->
<template>
  <Background :imagePath="'/img/menu-background.svg'" :style="'backgroundColor:rgba(44, 55, 87, 1)'"/>
  <GameNotification />
  <TopSection/>
  <div class="section-container">
    <router-view />  <!-- Page content changes here -->
  </div>
  <GameMenu ref="gameMenu"/>
  <OpenChestOverlay ref="chestOverlay"/>
  <!--  <FourChestPopupOverlay/> -->
  <PlayerPopup/>
  
</template>

<style scoped>
.section-container {
  position: fixed;
  width: 100vw;
  display: flex;
  flex-direction: column;
  height: calc(100vh - calc(var(--base-unit) * (124 + 36))); /* Adjust height dynamically */
  
  overflow-y: auto; /* Make only the content inside scroll */
  overflow-x: hidden;
  top: calc(var(--base-unit) * 84);
  bottom: calc(var(--base-unit) * 76);
}
</style>
