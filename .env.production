# API Configuration
VITE_API_BASE_URL=https://wolf.jpegonapechain.com/api

# Telegram Configuration
# 生产环境中通常不需要fallback数据，因为会从实际的Telegram WebApp获取
# VITE_TELEGRAM_INIT_DATA_FALLBACK=

# DApp Portal SDK Configuration
# 注意：您需要从 @linenext/dapp-portal-sdk 获取实际的生产环境客户端ID
VITE_DAPP_CLIENT_ID=6390fd82-d5da-462f-84e9-4755ad33c04e

# Production Configuration
# 这个变量由Vite自动设置，用于生产模式检测
# DEV=false (自动设置，无需手动配置)
