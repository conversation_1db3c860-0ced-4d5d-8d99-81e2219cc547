{"skeleton": {"hash": "dv+FQzrtoGQ", "spine": "4.2.38", "x": -301.05, "y": -24, "width": 601, "height": 941, "images": "./Image/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 428.05, "rotation": -90}, {"name": "bone2", "parent": "bone", "x": -386.67, "y": -16.33}, {"name": "11", "parent": "bone2", "length": 206.62, "rotation": -178.33, "x": 170.42, "y": 13.33}, {"name": "12", "parent": "11", "length": 146.15, "rotation": -1.2, "x": 206.65, "y": 1.21}, {"name": "8", "parent": "12", "length": 257.24, "rotation": 0.87, "x": 180.75, "y": -3.91}, {"name": "7", "parent": "8", "length": 92.48, "rotation": 144.8, "x": 172.03, "y": 167.47}, {"name": "9", "parent": "7", "length": 51.86, "rotation": 13.78, "x": 93.26, "y": 0.52}, {"name": "10", "parent": "9", "length": 55.39, "rotation": 38.81, "x": 51.86}, {"name": "13", "parent": "8", "length": 95.39, "rotation": -161.84, "x": 223.87, "y": -138.25}, {"name": "14", "parent": "13", "length": 58.19, "rotation": 13.68, "x": 101, "y": -0.99}, {"name": "15", "parent": "14", "length": 68.76, "rotation": 4.63, "x": 58.19}, {"name": "1", "parent": "8", "length": 126.46, "rotation": -90.5, "x": 45.44, "y": 3.67}, {"name": "2", "parent": "1", "length": 75.88, "rotation": -90.14, "x": 25.17, "y": -71.57}, {"name": "4", "parent": "8", "x": 120.98, "y": 98.18}, {"name": "3", "parent": "8", "x": 141.17, "y": -96.61}, {"name": "5", "parent": "8", "length": 31.77, "rotation": 90.95, "x": 201.68, "y": -89.14}, {"name": "6", "parent": "8", "length": 41.91, "rotation": -92.21, "x": 168.52, "y": 94.53}, {"name": "16", "parent": "12", "length": 57.95, "rotation": 176.88, "x": 117.29, "y": 22.63}, {"name": "17", "parent": "16", "length": 47.33, "rotation": -1.67, "x": 99.73, "y": -16.79}, {"name": "18", "parent": "17", "length": 62.34, "rotation": 4.32, "x": 48.22, "y": 0.07}, {"name": "27", "parent": "12", "length": 155.65, "rotation": 141.19, "x": 66.35, "y": 109.44}, {"name": "31", "parent": "27", "length": 96.94, "rotation": 17.91, "x": 156.06, "y": 0.79}, {"name": "26", "parent": "31", "length": 48.1, "rotation": 19.37, "x": 98.08, "y": -0.52}, {"name": "29", "parent": "26", "length": 29.86, "rotation": 18.41, "x": 44.55, "y": -0.96}, {"name": "30", "parent": "29", "length": 37.41, "rotation": 20.88, "x": 29.86}, {"name": "28", "parent": "26", "length": 36.72, "rotation": 23.89, "x": 16.52, "y": 21.68}, {"name": "32", "parent": "28", "length": 28.34, "rotation": 23.44, "x": 36.72}, {"name": "33", "parent": "26", "length": 52.66, "rotation": 4.94, "x": 18.29, "y": -25.49}, {"name": "34", "parent": "33", "length": 49.4, "rotation": 24.07, "x": 53.55, "y": -0.06}, {"name": "21", "parent": "12", "length": 161.39, "rotation": -145.04, "x": 88.53, "y": -116.3}, {"name": "25", "parent": "21", "length": 103.72, "rotation": -12.17, "x": 162.84, "y": -0.01}, {"name": "20", "parent": "25", "length": 50, "rotation": -19.18, "x": 103.61, "y": 1.99}, {"name": "23", "parent": "20", "length": 36.12, "rotation": -9.38, "x": 30.77, "y": -2.87}, {"name": "24", "parent": "23", "length": 35.51, "rotation": -17.91, "x": 36.85, "y": -0.6}, {"name": "22", "parent": "20", "length": 30.1, "rotation": -28.95, "x": 16.86, "y": -19.91}, {"name": "35", "parent": "22", "length": 27.05, "rotation": -13.14, "x": 31.31, "y": 0.56}, {"name": "36", "parent": "20", "length": 43.34, "rotation": -6.73, "x": 20.99, "y": 28.54}, {"name": "37", "parent": "36", "length": 37, "rotation": -10.89, "x": 44.67, "y": 0.06}, {"name": "19", "parent": "11", "length": 118.26, "rotation": -155.25, "x": 60.9, "y": -66.97}, {"name": "38", "parent": "19", "length": 140.29, "rotation": -33.51, "x": 116.77}, {"name": "39", "parent": "bone2", "length": 79.68, "rotation": 54.18, "x": 350.05, "y": 113.12, "color": "ff4000ff", "icon": "ik"}, {"name": "40", "parent": "11", "length": 127.36, "rotation": 140.98, "x": 72.9, "y": 69.96}, {"name": "41", "parent": "40", "length": 141.39, "rotation": 50.14, "x": 126.83, "y": -0.4}, {"name": "42", "parent": "bone2", "length": 91.84, "rotation": -55.04, "x": 336.73, "y": -105.36, "color": "ff4000ff", "icon": "ik"}, {"name": "43", "parent": "11", "length": 85.42, "rotation": -157.87, "x": 8.1, "y": -144.3}, {"name": "44", "parent": "43", "length": 56.89, "rotation": 21.74, "x": 89.15, "y": 0.87}, {"name": "45", "parent": "44", "length": 40.46, "rotation": 19.83, "x": 59.07, "y": 1.06}, {"name": "46", "parent": "45", "length": 36.4, "rotation": -35.03, "x": 45.21, "y": -1.19}], "slots": [{"name": "31", "bone": "31", "attachment": "31"}, {"name": "30", "bone": "33", "attachment": "30"}, {"name": "29", "bone": "29", "attachment": "29"}, {"name": "28", "bone": "28", "attachment": "28"}, {"name": "27", "bone": "27", "attachment": "27"}, {"name": "26", "bone": "26", "attachment": "26"}, {"name": "25", "bone": "25", "attachment": "25"}, {"name": "24", "bone": "36", "attachment": "24"}, {"name": "23", "bone": "23", "attachment": "23"}, {"name": "22", "bone": "22", "attachment": "22"}, {"name": "21", "bone": "21", "attachment": "21"}, {"name": "20", "bone": "20", "attachment": "20"}, {"name": "19", "bone": "43", "attachment": "19"}, {"name": "18", "bone": "45", "attachment": "18"}, {"name": "17", "bone": "39", "attachment": "17"}, {"name": "16", "bone": "38", "attachment": "16"}, {"name": "15", "bone": "19", "attachment": "15"}, {"name": "14", "bone": "42", "attachment": "14"}, {"name": "13", "bone": "41", "attachment": "13"}, {"name": "12", "bone": "40", "attachment": "12"}, {"name": "11", "bone": "11", "attachment": "11"}, {"name": "10", "bone": "16", "attachment": "10"}, {"name": "9", "bone": "13", "attachment": "9"}, {"name": "7", "bone": "7", "attachment": "7"}, {"name": "8", "bone": "8", "attachment": "8"}, {"name": "6", "bone": "6", "attachment": "6"}, {"name": "5", "bone": "5", "attachment": "5"}, {"name": "4", "bone": "4", "attachment": "4"}, {"name": "3", "bone": "3", "attachment": "3"}, {"name": "2", "bone": "2", "attachment": "2"}, {"name": "1", "bone": "1", "attachment": "1"}], "ik": [{"name": "39", "bones": ["19", "38"], "target": "39", "bendPositive": false}, {"name": "42", "order": 1, "bones": ["40", "41"], "target": "42"}], "skins": [{"name": "default", "attachments": {"1": {"1": {"x": 2.6, "y": -28.44, "rotation": -0.85, "width": 337, "height": 213}}, "2": {"2": {"x": 32.28, "y": 5.4, "rotation": 89.29, "width": 201, "height": 93}}, "3": {"3": {"x": -3.95, "y": 0.58, "rotation": -91.35, "width": 76, "height": 76}}, "4": {"4": {"x": -4.17, "y": 1.32, "rotation": -91.35, "width": 76, "height": 74}}, "5": {"5": {"x": -10.78, "y": 15.72, "rotation": 177.71, "width": 94, "height": 50}}, "6": {"6": {"x": -1.07, "y": -12.34, "rotation": 0.87, "width": 93, "height": 47}}, "7": {"7": {"type": "mesh", "uvs": [0.90167, 0.12414, 0.99999, 0.20932, 0.99999, 0.42699, 0.93072, 0.58967, 0.7723, 0.72829, 0.61388, 0.86691, 0.57787, 1, 0.40145, 1, 0.14581, 0.89211, 0, 0.73631, 0, 0.53697, 0, 0.33763, 0.13861, 0.21391, 0.43745, 0.11309, 0.63548, 0, 0.81774, 0], "triangles": [10, 11, 12, 13, 14, 0, 1, 13, 0, 1, 2, 13, 0, 14, 15, 3, 4, 2, 2, 12, 13, 2, 10, 12, 4, 10, 2, 9, 10, 4, 5, 9, 4, 8, 9, 5, 7, 8, 5, 6, 7, 5], "vertices": [3, 6, -6.04, 8.2, 0.99891, 7, -94.62, 31.11, 0.00109, 8, -94.63, 116.04, 0, 2, 6, 1.46, 28.97, 0.97487, 7, -82.38, 49.5, 0.02513, 3, 6, 39.24, 54.32, 0.66817, 7, -39.65, 65.11, 0.31929, 8, -30.49, 108.09, 0.01255, 3, 6, 72.61, 65.6, 0.24847, 7, -4.55, 68.13, 0.66775, 8, -1.25, 88.44, 0.08378, 3, 6, 108.41, 64.24, 0.01658, 7, 29.89, 58.28, 0.65982, 8, 19.41, 59.18, 0.3236, 2, 7, 64.33, 48.43, 0.16221, 8, 40.08, 29.92, 0.83779, 2, 7, 92.1, 53.48, 0.00411, 8, 64.88, 16.45, 0.99589, 1, 8, 57.34, -5.77, 1, 2, 7, 90.64, -8.23, 0.06654, 8, 25.06, -30.73, 0.93346, 3, 6, 167.02, -20.13, 0.05073, 7, 66.71, -37.62, 0.63615, 8, -12, -38.63, 0.31312, 3, 6, 132.42, -43.34, 0.41908, 7, 27.58, -51.92, 0.57861, 8, -51.46, -25.24, 0.00231, 3, 6, 97.82, -66.54, 0.89504, 7, -11.55, -66.22, 0.10496, 8, -90.91, -11.86, 0, 3, 6, 66.07, -65.64, 0.99494, 7, -42.16, -57.78, 0.00506, 8, -109.47, 13.91, 0, 1, 6, 26.43, -44.36, 1, 1, 6, -7.87, -35.66, 1, 1, 6, -21.37, -15.53, 1], "hull": 16, "edges": [28, 26, 26, 24, 24, 22, 28, 30, 2, 0, 0, 30, 2, 4, 4, 6, 10, 12, 18, 16, 12, 14, 16, 14, 18, 20, 20, 22, 6, 8, 8, 10], "width": 133, "height": 209}}, "8": {"8": {"x": 101.62, "y": 6.33, "rotation": -91.35, "width": 569, "height": 424}}, "9": {"9": {"type": "mesh", "uvs": [0.33356, 0.16898, 0.62608, 0.30336, 0.91179, 0.45481, 1, 0.61266, 1, 0.80633, 1, 1, 0.55805, 1, 0.28594, 0.82809, 0.07506, 0.46121, 0, 0.18818, 0, 0, 0.1669, 0], "triangles": [9, 10, 11, 9, 11, 0, 8, 9, 0, 8, 0, 1, 7, 2, 3, 7, 8, 1, 7, 1, 2, 4, 7, 3, 6, 7, 4, 6, 4, 5], "vertices": [1, 9, 25.36, 25.18, 1, 3, 9, 69.7, 55.4, 0.82576, 10, -17.08, 62.19, 0.14921, 11, -70.01, 68.06, 0.02503, 3, 9, 117.51, 83.33, 0.30729, 10, 35.98, 78.03, 0.37343, 11, -15.85, 79.56, 0.31928, 3, 9, 156.98, 83.2, 0.10185, 10, 74.3, 68.57, 0.2068, 11, 21.58, 67.04, 0.69136, 3, 9, 200.07, 67.94, 0.00723, 10, 112.55, 43.55, 0.00886, 11, 57.7, 39.03, 0.9839, 1, 11, 93.81, 11.01, 1, 1, 11, 53.71, -40.67, 1, 2, 10, 59.02, -47.71, 0.40564, 11, -3.03, -47.62, 0.59436, 2, 9, 77.59, -33.91, 0.84488, 10, -30.53, -26.45, 0.15512, 1, 9, 13.15, -22.87, 1, 1, 9, -28.72, -8.04, 1, 1, 9, -20.47, 15.24, 1], "hull": 12, "edges": [18, 20, 18, 16, 16, 14, 10, 12, 14, 12, 20, 22, 22, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10], "width": 148, "height": 236}}, "10": {"10": {"type": "mesh", "uvs": [1, 0.14441, 0.93461, 0.18986, 1, 0.28883, 1, 0.32841, 0.88945, 0.37394, 0.78429, 0.34777, 0.73846, 0.50258, 0.59837, 0.70755, 0.4596, 0.8929, 0.35224, 1, 0.22786, 1, 0.17811, 0.85801, 0.13491, 0.67485, 0.07861, 0.46769, 0, 0.27363, 0, 0.13681, 0.08006, 0.0372, 0.22038, 0, 0.20968, 0.25122, 0.41646, 0.24726, 0.5567, 0.22351, 0.65177, 0.17403, 0.66722, 0, 0.76929, 0.03556, 0.83361, 0, 1, 0], "triangles": [8, 9, 11, 9, 10, 11, 8, 11, 12, 12, 13, 18, 19, 12, 18, 8, 12, 19, 7, 19, 20, 7, 8, 19, 6, 20, 21, 6, 7, 20, 6, 21, 5, 13, 14, 18, 3, 4, 2, 4, 5, 1, 21, 23, 5, 1, 23, 24, 1, 5, 23, 4, 1, 2, 14, 16, 18, 14, 15, 16, 18, 16, 17, 0, 1, 25, 21, 22, 23, 1, 24, 25], "vertices": [1, 18, -48.69, 302.88, 1, 1, 18, -34.22, 272.26, 1, 1, 18, -7.29, 304.79, 1, 1, 18, 4.06, 305.32, 1, 1, 18, 19.55, 253.13, 1, 1, 18, 14.36, 202.57, 1, 3, 18, 59.76, 182.74, 0.78556, 19, -45.78, 198.28, 0.20886, 20, -78.82, 204.72, 0.00558, 3, 18, 121.61, 118.56, 0.54718, 19, 17.91, 135.93, 0.36948, 20, -20, 137.76, 0.08334, 3, 18, 177.8, 54.75, 0.11823, 19, 75.95, 73.79, 0.4072, 20, 33.2, 71.43, 0.47457, 3, 18, 210.88, 4.9, 0.00437, 19, 110.46, 24.93, 0.00192, 20, 63.94, 20.11, 0.99371, 3, 18, 213.62, -54.49, 0, 19, 114.94, -34.35, 0.01024, 20, 63.94, -39.34, 0.98976, 3, 18, 174.01, -80.12, 0.00021, 19, 76.09, -61.13, 0.31946, 20, 23.19, -63.12, 0.68034, 3, 18, 122.45, -103.18, 0.09399, 19, 25.23, -85.68, 0.78217, 20, -29.38, -83.78, 0.12384, 3, 18, 64.3, -132.8, 0.3964, 19, -32.03, -116.99, 0.60259, 20, -88.84, -110.69, 0.00101, 1, 18, 10.39, -172.9, 1, 1, 18, -28.83, -174.71, 1, 1, 18, -59.15, -137.8, 1, 1, 18, -72.91, -71.29, 1, 1, 18, -0.65, -73.08, 1, 1, 18, -6.35, 25.61, 1, 1, 18, -16.25, 92.25, 1, 1, 18, -32.53, 136.99, 1, 1, 18, -82.76, 142.07, 1, 1, 18, -74.81, 191.28, 1, 1, 18, -86.43, 221.52, 1, 1, 18, -90.09, 300.97, 1], "hull": 26, "edges": [28, 26, 26, 24, 24, 22, 22, 20, 18, 20, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 0, 50, 2, 0, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 28, 30, 32, 30, 44, 46, 48, 50, 46, 48], "width": 478, "height": 287}}, "11": {"11": {"type": "mesh", "uvs": [0.92532, 0.17969, 1, 0.43732, 1, 0.57799, 1, 0.71866, 0.85771, 0.90441, 0.65918, 1, 0.31837, 1, 0.11065, 0.91693, 0, 0.79349, 0, 0.68257, 0, 0.5, 0.06165, 0.22442, 0.11814, 0.05088, 0.29886, 0.03124, 0.59735, 1e-05, 0.82875, 0], "triangles": [7, 9, 10, 7, 8, 9, 3, 4, 2, 10, 13, 7, 2, 13, 1, 13, 6, 7, 6, 13, 2, 2, 5, 6, 4, 5, 2, 1, 14, 0, 14, 1, 13, 11, 13, 10, 13, 11, 12, 0, 14, 15], "vertices": [2, 4, 65.51, -163.45, 0.6359, 3, 268.72, -163.58, 0.3641, 2, 4, -34.17, -190.92, 0.20925, 3, 168.49, -188.96, 0.79075, 2, 4, -88.46, -190.48, 0.15143, 3, 114.22, -187.37, 0.84857, 2, 4, -142.76, -190.03, 0.06286, 3, 59.94, -185.79, 0.93714, 1, 3, -10.15, -129.78, 1, 1, 3, -44.83, -53.5, 1, 2, 4, -249.22, 69.2, 0.00248, 3, -41.06, 75.62, 0.99752, 2, 4, -216.51, 147.66, 0.03918, 3, -6.71, 153.37, 0.96082, 2, 4, -168.51, 189.2, 0.09473, 3, 42.15, 193.9, 0.90527, 2, 4, -125.7, 188.84, 0.16077, 3, 84.94, 192.65, 0.83923, 2, 4, -55.23, 188.26, 0.38306, 3, 155.39, 190.59, 0.61694, 2, 4, 50.95, 164.02, 0.8545, 3, 261.03, 164.12, 0.1455, 2, 4, 117.76, 142.06, 0.98474, 3, 327.36, 140.76, 0.01526, 1, 4, 124.77, 73.5, 1, 2, 4, 135.89, -39.72, 0.8434, 3, 341.68, -41.35, 0.1566, 1, 4, 135.17, -127.42, 1], "hull": 16, "edges": [24, 22, 22, 20, 28, 30, 30, 0, 0, 2, 16, 14, 14, 12, 10, 12, 10, 8, 8, 6, 2, 4, 4, 6, 16, 18, 18, 20, 24, 26, 26, 28], "width": 379, "height": 386}}, "12": {"12": {"type": "mesh", "uvs": [0.80482, 0.0893, 0.94516, 0.21974, 1, 0.33714, 1, 0.53281, 0.94516, 0.74339, 0.81284, 0.91297, 0.57427, 1, 0.27956, 0.92788, 0.08309, 0.82911, 0, 0.59431, 0, 0.41727, 0, 0.24024, 0.06905, 0.05203, 0.23946, 0, 0.57026, 0], "triangles": [6, 7, 5, 5, 7, 3, 3, 7, 8, 4, 5, 3, 14, 0, 3, 14, 3, 8, 14, 9, 13, 14, 8, 9, 11, 12, 13, 1, 3, 0, 13, 9, 10, 1, 2, 3, 13, 10, 11], "vertices": [1, 42, -42.03, 24.32, 1, 2, 42, -36.45, 65.82, 0.99387, 43, -53.81, 167.78, 0.00613, 2, 42, -22.28, 91.19, 0.97688, 43, -25.26, 173.17, 0.02312, 2, 42, 13.02, 118.13, 0.90833, 43, 18.06, 163.33, 0.09167, 2, 42, 58.04, 137.94, 0.76123, 43, 62.11, 141.46, 0.23877, 2, 42, 105.58, 139.09, 0.56896, 43, 93.47, 105.72, 0.43104, 2, 42, 151.83, 111.06, 0.30703, 43, 101.59, 52.25, 0.69297, 1, 43, 71.85, -4.76, 1, 2, 42, 183.87, 5.14, 0.00032, 43, 40.81, -40.23, 0.99968, 2, 42, 152.13, -41.13, 0.00228, 43, -15.05, -45.52, 0.99772, 2, 42, 120.18, -65.51, 0.34113, 43, -54.24, -36.62, 0.65887, 2, 42, 88.24, -89.89, 0.66654, 43, -93.43, -27.72, 0.33346, 2, 42, 45.43, -104.23, 0.82468, 43, -131.87, -4.06, 0.17532, 2, 42, 14.23, -82.81, 0.90534, 43, -135.42, 33.62, 0.09466, 2, 42, -28.11, -27.32, 0.99694, 43, -119.97, 101.69, 0.00306], "hull": 15, "edges": [18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 28, 0, 0, 2, 6, 4, 2, 4, 26, 28, 26, 24, 24, 22, 18, 20, 20, 22], "width": 211, "height": 227}}, "13": {"13": {"type": "mesh", "uvs": [0.75141, 0.08144, 0.92578, 0.21246, 1, 0.31325, 1, 0.5, 0.86301, 0.60956, 0.71653, 0.86152, 0.66306, 1, 0.45846, 1, 0.44684, 0.90788, 0.13529, 0.89377, 0.08414, 0.79702, 0.13994, 0.67003, 0.09344, 0.54707, 0, 0.39186, 0, 0.19593, 0.17946, 1e-05, 0.53751, 0], "triangles": [5, 6, 8, 6, 7, 8, 4, 5, 8, 11, 8, 9, 9, 10, 11, 4, 8, 11, 11, 0, 4, 0, 11, 16, 14, 15, 16, 16, 11, 12, 3, 4, 2, 4, 0, 1, 16, 12, 13, 4, 1, 2, 16, 13, 14], "vertices": [2, 43, -13.85, 104.97, 0.32309, 42, 37.38, 56.23, 0.67691, 2, 43, 21.3, 130.78, 0.55279, 42, 40.09, 99.76, 0.44721, 2, 43, 45.83, 139.59, 0.63854, 42, 49.05, 124.24, 0.36146, 2, 43, 85.53, 130.58, 0.74682, 42, 81.41, 148.94, 0.25318, 2, 43, 103.09, 100.04, 0.85325, 42, 116.1, 142.85, 0.14675, 2, 43, 150.52, 60.88, 0.98991, 42, 176.56, 154.16, 0.01009, 2, 43, 177.72, 44.34, 0.99977, 42, 206.69, 164.44, 0.00023, 1, 43, 169.16, 6.63, 1, 2, 43, 149.09, 8.94, 0.99997, 42, 215.52, 119.77, 3e-05, 1, 43, 133.05, -47.8, 1, 2, 43, 110.35, -52.56, 0.99996, 42, 237.9, 50.62, 4e-05, 2, 43, 85.68, -36.14, 0.98913, 42, 209.49, 42.21, 0.01087, 2, 43, 57.6, -38.78, 0.89227, 42, 193.51, 18.96, 0.10773, 2, 43, 20.69, -48.51, 0.60662, 42, 177.33, -15.61, 0.39338, 2, 43, -20.96, -39.05, 0.74295, 42, 143.38, -41.52, 0.25705, 2, 43, -55.1, 3.48, 0.18903, 42, 88.85, -40.47, 0.81097, 2, 43, -40.12, 69.47, 0.05623, 42, 47.79, 13.32, 0.94377], "hull": 17, "edges": [26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 12, 14, 12, 10, 10, 8, 8, 6, 32, 0, 0, 2, 6, 4, 2, 4, 30, 32, 26, 28, 30, 28], "width": 189, "height": 218}}, "14": {"14": {"x": 42.71, "y": -6.74, "rotation": 145.04, "width": 181, "height": 110}}, "15": {"15": {"type": "mesh", "uvs": [0.89283, 0.07585, 0.97464, 0.20921, 1, 0.42228, 1, 0.57982, 1, 0.73736, 0.87781, 0.89985, 0.64408, 0.95503, 0.31686, 0.96423, 0.14825, 0.90904, 0.04307, 0.74349, 0, 0.55801, 0, 0.3625, 0.05142, 0.19848, 0.19499, 0.07125, 0.36461, 0, 0.72923, 0], "triangles": [6, 7, 2, 5, 6, 2, 8, 2, 7, 13, 2, 9, 2, 13, 14, 2, 8, 9, 11, 12, 13, 4, 5, 3, 1, 15, 0, 14, 15, 2, 13, 9, 10, 3, 5, 2, 2, 15, 1, 13, 10, 11], "vertices": [1, 39, 21.97, 98.68, 1, 1, 39, 55.6, 100.42, 1, 1, 39, 99.86, 84.15, 1, 1, 39, 130.9, 68.73, 1, 2, 40, 8.22, 69.38, 0.03291, 39, 161.93, 53.31, 0.96709, 2, 40, 46.74, 49.3, 0.40277, 39, 182.96, 15.3, 0.59723, 2, 40, 64.62, 3.95, 0.99379, 39, 172.83, -32.39, 0.00621, 2, 40, 74.79, -61.39, 0.95589, 39, 145.23, -92.48, 0.04411, 2, 40, 66.94, -96.69, 0.85757, 39, 119.2, -117.58, 0.14243, 2, 40, 33.42, -122.27, 0.65966, 39, 77.13, -120.4, 0.34034, 2, 40, -6, -135.94, 0.4003, 39, 36.72, -110.04, 0.5997, 2, 40, -48.68, -141.25, 0.17617, 39, -1.8, -90.9, 0.82383, 2, 40, -85.77, -135.4, 0.06645, 39, -29.5, -65.54, 0.93355, 2, 40, -117.13, -110.07, 0.01136, 39, -41.66, -27.11, 0.98864, 1, 39, -40.45, 10.55, 1, 1, 39, -7.68, 76.51, 1], "hull": 16, "edges": [20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 30, 0, 0, 2, 2, 4, 4, 6, 6, 8, 28, 30, 28, 26, 26, 24, 20, 22, 24, 22], "width": 202, "height": 220}}, "16": {"16": {"type": "mesh", "uvs": [0.93019, 0.09651, 1, 0.20627, 1, 0.35812, 0.90067, 0.56861, 0.84509, 0.70092, 0.91456, 0.75355, 0.89893, 0.88135, 0.69053, 0.89789, 0.52902, 0.9024, 0.52728, 1, 0.29109, 1, 0.23378, 0.76407, 0.11569, 0.57162, 0, 0.43179, 0, 0.31903, 0.09658, 0.18221, 0.28588, 0.06193, 0.50123, 0, 0.78084, 0], "triangles": [9, 10, 8, 10, 11, 8, 7, 8, 4, 7, 4, 6, 4, 8, 11, 6, 4, 5, 4, 11, 3, 16, 3, 12, 3, 16, 17, 3, 11, 12, 2, 3, 17, 12, 14, 15, 12, 15, 16, 2, 18, 0, 18, 2, 17, 12, 13, 14, 0, 1, 2], "vertices": [2, 40, -48.09, 49.22, 0.00118, 39, 103.85, 67.59, 0.99882, 2, 40, -26.18, 65.11, 0, 39, 130.89, 68.73, 1, 2, 40, 6.37, 69.15, 0.07953, 39, 160.26, 54.14, 0.92047, 2, 40, 53.78, 56.33, 0.68108, 39, 192.72, 17.27, 0.31892, 2, 40, 83.43, 49.55, 0.94902, 39, 213.69, -4.75, 0.05098, 2, 40, 93.1, 63.84, 0.98714, 39, 229.65, 1.82, 0.01286, 2, 40, 120.86, 64.35, 0.99985, 39, 253.07, -13.08, 0.00015, 1, 40, 129.21, 26.12, 1, 1, 40, 133.91, -3.73, 1, 1, 40, 154.87, -1.45, 1, 1, 40, 160.32, -45.28, 1, 2, 40, 111.07, -62.21, 0.98569, 39, 175.04, -113.2, 0.01431, 2, 40, 72.55, -89.25, 0.85038, 39, 127.98, -114.48, 0.14962, 2, 40, 45.25, -114.45, 0.69326, 39, 91.31, -120.41, 0.30674, 2, 40, 21.08, -117.46, 0.61018, 39, 69.5, -109.57, 0.38982, 2, 40, -10.48, -103.18, 0.44736, 39, 51.07, -80.25, 0.55264, 2, 40, -40.63, -71.26, 0.16225, 39, 43.55, -36.99, 0.83775, 1, 39, 49.49, 5.03, 1, 2, 40, -65.33, 18.93, 0.00321, 39, 72.75, 51.85, 0.99679], "hull": 19, "edges": [26, 24, 24, 22, 22, 20, 18, 20, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 36, 0, 4, 2, 0, 2, 34, 36, 34, 32, 32, 30, 26, 28, 30, 28], "width": 187, "height": 216}}, "17": {"17": {"x": 33.6, "y": 16.08, "rotation": 35.82, "width": 179, "height": 108}}, "18": {"18": {"type": "mesh", "uvs": [0.70031, 0.11663, 0.90872, 0.32297, 1, 0.5, 1, 0.65124, 1, 0.80249, 0.92177, 1, 0.72507, 1, 0.64459, 0.79287, 0.3846, 0.7561, 0.15968, 0.70706, 0, 0.46599, 0, 0.10641, 0.12666, 0, 0.5, 0], "triangles": [10, 11, 12, 7, 8, 1, 13, 10, 12, 13, 8, 10, 0, 8, 13, 1, 8, 0, 8, 9, 10, 7, 1, 2, 3, 7, 2, 7, 3, 4, 6, 7, 4, 5, 6, 4], "vertices": [2, 47, 37.54, 36.43, 0.80339, 48, -27.88, 26.4, 0.19661, 2, 47, 65.17, 26.17, 0.16724, 48, 0.64, 33.85, 0.83276, 2, 47, 80.92, 13.71, 0.00709, 48, 20.68, 32.7, 0.99291, 1, 48, 33.86, 24.98, 1, 1, 48, 47.04, 17.27, 1, 1, 48, 60.31, 0.44, 1, 1, 48, 50.37, -16.54, 1, 3, 46, 125.88, -4.59, 0.00029, 47, 60.93, -27.98, 0.03314, 48, 28.25, -12.92, 0.96656, 3, 46, 104.72, -20.15, 0.05168, 47, 35.75, -35.44, 0.50797, 48, 11.91, -33.48, 0.44035, 3, 46, 85.2, -32.36, 0.22188, 47, 13.24, -40.31, 0.65725, 48, -3.72, -50.39, 0.12087, 3, 46, 56.75, -26.17, 0.70721, 47, -11.42, -24.83, 0.2872, 48, -32.8, -51.87, 0.00559, 1, 46, 31.31, -0.24, 1, 2, 46, 32.83, 16.3, 0.7203, 47, -19.51, 23.23, 0.2797, 3, 46, 59.47, 42.45, 0.01048, 47, 14.42, 38.79, 0.97859, 48, -48.16, 15.06, 0.01093], "hull": 14, "edges": [20, 18, 18, 16, 16, 14, 14, 12, 26, 0, 0, 2, 2, 4, 24, 26, 20, 22, 24, 22, 10, 12, 8, 10, 4, 6, 6, 8], "width": 100, "height": 101}}, "19": {"19": {"type": "mesh", "uvs": [0.49457, 0.23858, 0.54799, 0.4433, 0.6914, 0.60964, 0.84043, 0.75252, 0.99999, 0.81203, 0.99999, 0.97014, 0.88085, 0.99999, 0.73358, 0.99999, 0.59001, 0.91998, 0.38899, 0.79106, 0.14027, 0.61604, 0, 0.37293, 0, 0.18646, 0.19807, 1e-05, 0.45239, 0], "triangles": [0, 11, 12, 11, 1, 10, 13, 14, 0, 13, 0, 12, 1, 9, 10, 0, 1, 11, 9, 1, 2, 8, 9, 2, 3, 8, 2, 4, 6, 3, 3, 7, 8, 4, 5, 6, 6, 7, 3], "vertices": [1, 45, 33.68, 26.5, 1, 2, 45, 64.03, 19.71, 0.97317, 46, -16.36, 26.81, 0.02683, 3, 45, 93.24, 24.54, 0.15042, 46, 12.57, 20.47, 0.8494, 47, -37.16, 34.03, 0.00018, 2, 46, 39.5, 17.07, 0.79207, 47, -12.98, 21.69, 0.20793, 2, 46, 58.58, 23.37, 0.13793, 47, 7.11, 21.15, 0.86207, 1, 47, 16.93, -0.27, 1, 2, 46, 68.58, -6.05, 0.34456, 47, 6.54, -9.92, 0.65544, 2, 46, 56.7, -17.71, 0.99864, 47, -8.59, -16.86, 0.00136, 1, 46, 36.78, -20.56, 1, 2, 45, 104.18, -17.64, 0.0156, 46, 7.11, -22.76, 0.9844, 2, 45, 68.98, -32.83, 0.86073, 46, -31.22, -23.83, 0.13927, 1, 45, 29.44, -32.71, 1, 1, 45, 4.02, -21.49, 1, 1, 45, -12.37, 10.2, 1, 1, 45, -0.77, 36.49, 1], "hull": 15, "edges": [22, 20, 20, 18, 18, 16, 16, 14, 28, 0, 0, 2, 2, 4, 4, 6, 10, 8, 6, 8, 22, 24, 26, 28, 24, 26, 10, 12, 12, 14], "width": 113, "height": 149}}, "20": {"20": {"type": "mesh", "uvs": [0.91666, 0.27599, 1, 0.61502, 1, 0.80751, 0.78187, 1, 0.23978, 1, 0, 0.85553, 0, 0.55417, 0.06983, 0.24122, 0.29253, 0, 0.77015, 0], "triangles": [2, 3, 1, 1, 3, 4, 0, 7, 8, 8, 9, 0, 6, 1, 4, 7, 0, 6, 4, 5, 6, 0, 1, 6], "vertices": [2, 31, 107.64, 41.7, 0.03226, 32, -9.24, 38.83, 0.96774, 1, 32, 21.73, 44.05, 1, 1, 32, 39.01, 42.82, 1, 1, 32, 54.9, 22.22, 1, 2, 31, 143.71, -39.38, 0.20526, 32, 51.46, -25.9, 0.79474, 2, 31, 123.33, -53.85, 0.49327, 32, 36.97, -46.26, 0.50673, 2, 31, 98.42, -43.14, 0.83515, 32, 9.92, -44.33, 0.16485, 2, 31, 75, -26.31, 0.99988, 32, -17.73, -36.12, 0.00012, 2, 31, 62.88, 0.48, 0.99977, 32, -37.97, -14.81, 0.00023, 2, 31, 79.67, 39.53, 0.32106, 32, -34.94, 27.59, 0.67894], "hull": 10, "edges": [16, 14, 14, 12, 16, 18, 18, 0, 0, 2, 2, 4, 4, 6, 10, 12, 6, 8, 10, 8], "width": 89, "height": 90}}, "21": {"21": {"type": "mesh", "uvs": [0.59535, 0.06786, 0.81342, 0.27719, 1, 0.49753, 1, 0.74779, 0.95258, 0.91619, 0.81014, 1, 0.62027, 1, 0.46658, 0.9099, 0.29835, 0.75565, 0.12182, 0.56521, 0, 0.37634, 0, 0.12452, 0.14051, 0, 0.43751, 0], "triangles": [5, 6, 4, 6, 7, 4, 4, 7, 3, 7, 8, 3, 1, 8, 9, 3, 8, 2, 1, 9, 0, 12, 13, 11, 0, 10, 13, 8, 1, 2, 0, 9, 10, 13, 10, 11], "vertices": [2, 31, -166.21, 36.34, 0.00164, 30, 8.03, 70.54, 0.99836, 2, 31, -100.27, 54.04, 0.10016, 30, 76.22, 73.95, 0.89984, 2, 31, -34.15, 65.01, 0.59228, 30, 143.16, 70.74, 0.40772, 1, 31, 24.7, 39.71, 1, 1, 31, 60.68, 14.23, 1, 2, 31, 69.47, -19.63, 0.92482, 30, 226.62, -33.85, 0.07518, 2, 31, 54.93, -53.47, 0.64464, 30, 205.26, -63.86, 0.35536, 2, 31, 21.96, -71.75, 0.26628, 30, 169.18, -74.78, 0.73372, 2, 31, -27.21, -86.14, 0.00633, 30, 118.09, -78.48, 0.99367, 1, 30, 58.51, -78.12, 1, 1, 30, 5.41, -69.35, 1, 1, 30, -47.11, -31.97, 1, 1, 30, -57.28, 8.72, 1, 1, 30, -23.88, 55.67, 1], "hull": 14, "edges": [20, 18, 18, 16, 16, 14, 14, 12, 10, 12, 10, 8, 8, 6, 26, 0, 0, 2, 6, 4, 2, 4, 24, 26, 20, 22, 24, 22], "width": 194, "height": 256}}, "22": {"22": {"type": "mesh", "uvs": [1, 0.1377, 1, 0.32245, 0.7761, 0.81114, 0.41566, 1, 0.12965, 1, 0, 0.92735, 0, 0.63235, 0.25111, 0.23603, 0.5, 0, 0.85446, 0], "triangles": [2, 3, 6, 6, 3, 4, 4, 5, 6, 6, 7, 2, 2, 7, 1, 0, 1, 8, 8, 1, 7, 8, 9, 0], "vertices": [1, 35, -8.02, 11.88, 1, 2, 35, 3.88, 17.39, 0.98858, 36, -30.54, 10.16, 0.01142, 2, 35, 40.44, 21.01, 0.055, 36, 4.25, 21.99, 0.945, 1, 36, 26.8, 14.91, 1, 1, 36, 36.31, 2.74, 1, 2, 35, 65.55, -13.54, 0.00053, 36, 36.55, -5.95, 0.99947, 2, 35, 46.55, -22.35, 0.19471, 36, 20.05, -18.85, 0.80529, 2, 35, 15.32, -21.88, 0.99394, 36, -10.47, -25.49, 0.00606, 1, 35, -5.54, -16.73, 1, 1, 35, -13.59, 0.64, 1], "hull": 10, "edges": [16, 14, 14, 12, 16, 18, 18, 0, 0, 2, 2, 4, 4, 6, 10, 12, 6, 8, 10, 8], "width": 54, "height": 71}}, "23": {"23": {"type": "mesh", "uvs": [1, 0.25019, 1, 0.62509, 0.75705, 0.87538, 0.40785, 1, 0, 1, 0, 0.83145, 0, 0.58232, 0.19489, 0.28533, 0.38292, 0, 0.69146, 0], "triangles": [2, 3, 5, 3, 4, 5, 1, 2, 6, 1, 6, 7, 1, 7, 0, 9, 0, 7, 6, 2, 5, 7, 8, 9], "vertices": [1, 33, -4.07, 22.89, 1, 2, 33, 35.87, 26.58, 0.97328, 34, -9.29, 25.57, 0.02672, 2, 33, 63.79, 15.51, 0.20321, 34, 20.68, 23.61, 0.79679, 2, 33, 78.87, -2.74, 0.00257, 34, 40.64, 10.89, 0.99743, 1, 34, 49.64, -10.1, 1, 1, 34, 33.06, -17.21, 1, 2, 33, 36.48, -29.6, 0.14802, 34, 8.56, -27.71, 0.85198, 2, 33, 3.83, -21.66, 0.9287, 34, -24.94, -30.19, 0.0713, 1, 33, -27.54, -13.99, 1, 1, 33, -29.14, 3.21, 1], "hull": 10, "edges": [16, 14, 14, 12, 16, 18, 18, 0, 0, 2, 2, 4, 6, 8, 4, 6, 8, 10, 10, 12], "width": 56, "height": 107}}, "24": {"24": {"type": "mesh", "uvs": [0.9334, 0.16453, 1, 0.5, 0.85884, 0.80349, 0.43091, 1, 0.1132, 1, 0, 0.9271, 0, 0.76518, 0, 0.5, 0.16831, 0.16801, 0.5, 0, 0.75, 0], "triangles": [2, 3, 6, 5, 6, 3, 3, 4, 5, 1, 2, 7, 1, 7, 8, 8, 0, 1, 8, 9, 0, 9, 10, 0, 7, 2, 6], "vertices": [1, 37, 2.09, 20.78, 1, 2, 37, 38.1, 26.31, 0.73375, 38, -11.41, 24.53, 0.26625, 2, 37, 71.22, 19.64, 0.01371, 38, 22.37, 24.24, 0.98629, 1, 38, 48.81, 5.08, 1, 2, 37, 94.41, -22.58, 0.00234, 38, 53.12, -12.83, 0.99766, 2, 37, 86.85, -29.5, 0.01641, 38, 47.01, -21.06, 0.98359, 2, 37, 69.38, -30.31, 0.15669, 38, 30.01, -25.15, 0.84331, 2, 37, 40.77, -31.63, 0.85327, 38, 2.16, -31.86, 0.14673, 1, 37, 4.51, -23.53, 1, 1, 37, -14.5, -5.15, 1, 1, 37, -15.17, 9.33, 1], "hull": 11, "edges": [18, 16, 16, 14, 18, 20, 20, 0, 0, 2, 2, 4, 4, 6, 6, 8, 10, 8, 10, 12, 12, 14], "width": 58, "height": 108}}, "25": {"25": {"type": "mesh", "uvs": [0.79262, 0.17735, 0.91925, 0.43069, 1, 0.68738, 1, 0.90046, 0.8415, 1, 0.59016, 1, 0.38355, 0.81587, 0.15695, 0.60112, 0, 0.37462, 0, 0.13205, 0.22612, 0, 0.66155, 0], "triangles": [3, 4, 2, 2, 4, 5, 5, 6, 2, 1, 6, 7, 6, 1, 2, 1, 7, 0, 0, 10, 11, 8, 10, 0, 0, 7, 8, 8, 9, 10], "vertices": [2, 31, -13.47, 58.22, 0.86316, 30, 161.95, 59.74, 0.13684, 1, 31, 38.47, 55.88, 1, 1, 31, 88.37, 47.17, 1, 1, 31, 125.95, 31.01, 1, 1, 31, 134.44, 2.35, 1, 1, 31, 120.04, -31.13, 1, 2, 31, 75.73, -44.69, 0.95303, 30, 227.45, -59.67, 0.04697, 2, 31, 24.87, -58.59, 0.47288, 30, 174.81, -62.53, 0.52712, 2, 31, -24.07, -62.32, 0.02063, 30, 126.18, -55.86, 0.97937, 1, 30, 88.23, -28.86, 1, 2, 31, -77.2, -3.79, 0.01319, 30, 86.58, 12.55, 0.98681, 2, 31, -52.26, 54.21, 0.51911, 30, 123.19, 63.99, 0.48089], "hull": 12, "edges": [16, 14, 14, 12, 12, 10, 22, 0, 0, 2, 2, 4, 4, 6, 8, 10, 6, 8, 16, 18, 20, 22, 18, 20], "width": 145, "height": 192}}, "26": {"26": {"type": "mesh", "uvs": [0.93744, 0.26487, 1, 0.5, 1, 0.75, 0.80515, 1, 0.25098, 1, 0, 0.89035, 0, 0.60291, 0, 0.31548, 0.12584, 0, 0.71577, 0], "triangles": [2, 3, 1, 1, 4, 7, 1, 3, 4, 0, 7, 9, 9, 7, 8, 4, 5, 6, 1, 7, 0, 4, 6, 7], "vertices": [2, 23, -9.52, 37.3, 0.08737, 22, 76.73, 31.51, 0.91263, 2, 23, 11.3, 43.31, 0.39873, 22, 94.38, 44.09, 0.60127, 2, 23, 33.55, 43.73, 0.70812, 22, 115.23, 51.86, 0.29188, 2, 23, 56.12, 26.61, 0.93125, 22, 142.2, 43.19, 0.06875, 1, 23, 57.04, -23.26, 1, 1, 23, 47.7, -46.03, 1, 1, 23, 22.13, -46.5, 1, 2, 23, -3.45, -46.97, 0.96104, 22, 110.41, -45.98, 0.03896, 2, 23, -31.73, -36.17, 0.71251, 22, 80.14, -45.17, 0.28749, 2, 23, -32.72, 16.91, 0.00013, 22, 61.61, 4.58, 0.99987], "hull": 10, "edges": [16, 14, 16, 18, 18, 0, 0, 2, 2, 4, 4, 6, 6, 8, 10, 8, 10, 12, 12, 14], "width": 90, "height": 89}}, "27": {"27": {"type": "mesh", "uvs": [1, 0.0904, 1, 0.27766, 1, 0.46492, 0.81728, 0.67669, 0.6018, 0.89238, 0.40347, 1, 0.14392, 1, 0.05577, 0.92572, 0, 0.77756, 0, 0.55512, 0.11944, 0.37472, 0.28594, 0.18844, 0.44754, 0.04138, 0.56997, 0, 0.88829, 0], "triangles": [8, 4, 5, 5, 6, 7, 5, 7, 8, 10, 3, 4, 4, 8, 9, 4, 9, 10, 10, 11, 3, 3, 11, 1, 1, 11, 12, 2, 3, 1, 13, 1, 12, 1, 13, 14, 14, 0, 1], "vertices": [1, 21, -67.63, 10.5, 1, 1, 21, -31.5, 39.07, 1, 2, 21, 4.63, 67.65, 0.99964, 22, -123.53, 110.17, 0.00036, 2, 21, 67.82, 71.73, 0.93205, 22, -62.15, 94.63, 0.06795, 2, 21, 135.77, 71.35, 0.43119, 22, 2.39, 73.37, 0.56881, 2, 21, 180.77, 57.12, 0.04053, 22, 40.84, 46, 0.95947, 2, 21, 212.49, 17.02, 0.00035, 22, 58.69, -1.91, 0.99965, 2, 21, 208.93, -7.94, 0.15149, 22, 47.63, -24.56, 0.84851, 2, 21, 187.16, -39.16, 0.78608, 22, 17.31, -47.58, 0.21392, 1, 21, 144.24, -73.11, 1, 1, 21, 94.83, -82.18, 1, 1, 21, 38.54, -84.88, 1, 1, 21, -9.58, -82.35, 1, 1, 21, -32.53, -69.74, 1, 1, 21, -71.42, -20.56, 1], "hull": 15, "edges": [26, 24, 24, 22, 22, 20, 20, 18, 16, 18, 16, 14, 14, 12, 10, 12, 10, 8, 8, 6, 6, 4, 26, 28, 28, 0, 0, 2, 2, 4], "width": 197, "height": 246}}, "28": {"28": {"type": "mesh", "uvs": [0.79361, 0.30507, 1, 0.58213, 1, 1, 0.5, 1, 0.16391, 0.75986, 0, 0.5, 0, 0.12471, 0.12111, 0, 0.5, 0], "triangles": [3, 1, 2, 3, 4, 1, 0, 4, 5, 4, 0, 1, 0, 5, 8, 7, 8, 6, 8, 5, 6], "vertices": [2, 26, 25.29, 24.67, 0.73764, 27, -0.68, 27.18, 0.26236, 2, 26, 47.63, 28.47, 0.15276, 27, 21.34, 21.78, 0.84724, 1, 27, 41.27, 0.95, 1, 2, 26, 62.76, -9.9, 0.01406, 27, 19.95, -19.44, 0.98594, 2, 26, 39.79, -21.75, 0.76184, 27, -5.83, -21.18, 0.23816, 1, 26, 19.51, -23.7, 1, 1, 26, -4.35, -13.66, 1, 1, 26, -9.51, -3.73, 1, 2, 26, -0.84, 16.87, 0.99988, 27, -27.74, 30.42, 0.00012], "hull": 9, "edges": [10, 8, 4, 6, 8, 6, 16, 0, 4, 2, 0, 2, 10, 12, 14, 16, 12, 14], "width": 59, "height": 69}}, "29": {"29": {"type": "mesh", "uvs": [0.73825, 0.26874, 0.89716, 0.55044, 1, 0.7744, 1, 1, 0.5, 1, 0.22262, 0.84074, 0, 0.61546, 0, 0.30773, 0.10749, 0, 0.5, 0], "triangles": [0, 6, 7, 7, 8, 9, 9, 0, 7, 1, 5, 6, 1, 6, 0, 5, 1, 2, 4, 5, 2, 4, 2, 3], "vertices": [3, 23, 36.78, 27.29, 0.78997, 24, 1.55, 29.26, 0.15673, 25, -16.02, 37.42, 0.05329, 3, 23, 65.61, 37.84, 0.07889, 24, 32.23, 30.16, 0.28424, 25, 12.97, 27.33, 0.63687, 3, 23, 88.55, 44.75, 0.00017, 24, 56.18, 29.46, 0.01882, 25, 35.1, 18.14, 0.98101, 1, 25, 53.35, 3.76, 1, 2, 23, 112.37, 13.68, 0.00067, 25, 33.86, -20.98, 0.99933, 3, 23, 96.29, -4.09, 0.07909, 24, 48.1, -19.32, 0.04164, 25, 10.16, -24.55, 0.87927, 3, 23, 73.35, -18.55, 0.62963, 24, 21.77, -25.79, 0.24787, 25, -16.75, -21.21, 0.12251, 2, 23, 41.66, -19.13, 0.5151, 24, -8.49, -16.33, 0.4849, 2, 23, 9.84, -12.95, 0.97711, 24, -36.72, -0.41, 0.02289, 2, 23, 9.39, 11.77, 1, 24, -29.34, 23.19, 0], "hull": 10, "edges": [12, 10, 6, 8, 10, 8, 18, 0, 0, 2, 6, 4, 2, 4, 16, 18, 12, 14, 16, 14], "width": 63, "height": 103}}, "30": {"30": {"type": "mesh", "uvs": [0.81559, 0.26897, 0.95149, 0.52114, 1, 0.78436, 1, 1, 0.5, 1, 0.22673, 0.83131, 0, 0.59755, 0, 0.32787, 0.10267, 1e-05, 0.5, 0], "triangles": [4, 2, 3, 4, 5, 2, 5, 1, 2, 1, 6, 0, 1, 5, 6, 9, 0, 7, 7, 8, 9, 0, 6, 7], "vertices": [2, 28, 26.88, 26.36, 0.99386, 29, -13.58, 35.01, 0.00614, 2, 28, 53.88, 33.25, 0.56362, 29, 13.89, 30.28, 0.43638, 2, 28, 81.67, 34.48, 0.04816, 29, 39.76, 20.07, 0.95184, 1, 29, 59.76, 9.45, 1, 1, 29, 44.76, -18.81, 1, 1, 29, 20.92, -25.96, 1, 2, 28, 57.77, -28.05, 0.075, 29, -7.57, -27.27, 0.925, 2, 28, 29.52, -26.13, 0.76727, 29, -32.58, -14, 0.23273, 2, 28, -4.38, -17.25, 0.99991, 29, -59.91, 7.94, 9e-05, 1, 28, -2.67, 8.12, 1], "hull": 10, "edges": [12, 10, 6, 8, 10, 8, 16, 18, 18, 0, 0, 2, 6, 4, 2, 4, 12, 14, 14, 16], "width": 64, "height": 105}}, "31": {"31": {"type": "mesh", "uvs": [0.9273, 0.07539, 1, 0.18833, 1, 0.37658, 0.89383, 0.59391, 0.68038, 0.77205, 0.56448, 0.91851, 0.42848, 0.99999, 0.16282, 1, 0, 0.87841, 0, 0.59563, 0.0471, 0.43535, 0.10521, 0.25641, 0.22368, 0.04972, 0.43047, 0, 0.71524, 0], "triangles": [7, 8, 6, 6, 8, 5, 4, 5, 9, 5, 8, 9, 9, 10, 4, 4, 10, 3, 3, 10, 11, 13, 3, 11, 2, 3, 13, 12, 13, 11, 1, 2, 14, 14, 2, 13, 14, 0, 1], "vertices": [2, 22, -70.08, 27.99, 0.00057, 21, 80.77, 5.89, 0.99943, 2, 22, -52.71, 44.78, 0.03909, 21, 92.13, 27.2, 0.96091, 2, 22, -18.13, 57.66, 0.37534, 21, 121.07, 50.09, 0.62466, 2, 22, 26.71, 59.3, 0.91604, 21, 163.24, 65.44, 0.08396, 1, 22, 69.34, 44.89, 1, 1, 22, 101.62, 40.47, 1, 1, 22, 122.9, 29.09, 1, 1, 22, 135.24, -4.02, 1, 2, 22, 120.47, -32.63, 0.99958, 21, 280.72, 6.78, 0.00042, 2, 22, 68.53, -51.98, 0.86373, 21, 237.25, -27.6, 0.13627, 2, 22, 36.91, -57.08, 0.52308, 21, 208.72, -42.17, 0.47692, 2, 22, 1.34, -62.08, 0.07905, 21, 176.42, -57.86, 0.92095, 1, 21, 134.87, -70.64, 1, 1, 21, 110.17, -55.11, 1, 1, 21, 86.67, -25.4, 1], "hull": 15, "edges": [26, 24, 26, 28, 28, 0, 0, 2, 2, 4, 16, 18, 12, 14, 16, 14, 22, 24, 18, 20, 20, 22, 4, 6, 6, 8, 8, 10, 10, 12], "width": 133, "height": 196}}}}], "animations": {"Idle": {"bones": {"11": {"translate": [{"curve": [0.222, 0, 0.444, 10.42, 0.222, 0, 0.444, 0]}, {"time": 0.6667, "x": 10.42, "curve": [0.889, 10.42, 1.111, 0, 0.889, 0, 1.111, 0]}, {"time": 1.3333, "curve": [1.556, 0, 1.778, 10.42, 1.556, 0, 1.778, 0]}, {"time": 2, "x": 10.42, "curve": [2.222, 10.42, 2.444, 0, 2.222, 0, 2.444, 0]}, {"time": 2.6667, "curve": [2.889, 0, 3.111, 10.42, 2.889, 0, 3.111, 0]}, {"time": 3.3333, "x": 10.42, "curve": [3.556, 10.42, 3.778, 0, 3.556, 0, 3.778, 0]}, {"time": 4}]}, "12": {"translate": [{"x": -0.18, "y": 0.01, "curve": [0.022, -0.06, 0.044, 0, 0.022, 0, 0.044, 0]}, {"time": 0.0667, "curve": [0.289, 0, 0.511, -6.25, 0.289, 0, 0.511, 0.18]}, {"time": 0.7333, "x": -6.25, "y": 0.18, "curve": [0.933, -6.25, 1.133, -1.19, 0.933, 0.18, 1.133, 0.03]}, {"time": 1.3333, "x": -0.18, "y": 0.01, "curve": [1.356, -0.06, 1.378, 0, 1.356, 0, 1.378, 0]}, {"time": 1.4, "curve": [1.622, 0, 1.844, -6.25, 1.622, 0, 1.844, 0.18]}, {"time": 2.0667, "x": -6.25, "y": 0.18, "curve": [2.267, -6.25, 2.467, -1.19, 2.267, 0.18, 2.467, 0.03]}, {"time": 2.6667, "x": -0.18, "y": 0.01, "curve": [2.689, -0.06, 2.711, 0, 2.689, 0, 2.711, 0]}, {"time": 2.7333, "curve": [2.956, 0, 3.178, -6.25, 2.956, 0, 3.178, 0.18]}, {"time": 3.4, "x": -6.25, "y": 0.18, "curve": [3.6, -6.25, 3.8, -1.19, 3.6, 0.18, 3.8, 0.03]}, {"time": 4, "x": -0.18, "y": 0.01}]}, "8": {"translate": [{"curve": [0.222, 0, 0.444, -9.38, 0.222, 0, 0.444, 0.08]}, {"time": 0.6667, "x": -9.38, "y": 0.08, "curve": [0.889, -9.38, 1.111, 0, 0.889, 0.08, 1.111, 0]}, {"time": 1.3333, "curve": [1.556, 0, 1.778, -9.38, 1.556, 0, 1.778, 0.08]}, {"time": 2, "x": -9.38, "y": 0.08, "curve": [2.222, -9.38, 2.444, 0, 2.222, 0.08, 2.444, 0]}, {"time": 2.6667, "curve": [2.889, 0, 3.111, -9.38, 2.889, 0, 3.111, 0.08]}, {"time": 3.3333, "x": -9.38, "y": 0.08, "curve": [3.556, -9.38, 3.778, 0, 3.556, 0.08, 3.778, 0]}, {"time": 4}]}, "27": {"rotate": [{"value": 0.32, "curve": [0.046, 0.13, 0.09, 0]}, {"time": 0.1333, "curve": [0.356, 0, 0.578, 3.04]}, {"time": 0.8, "value": 3.04, "curve": [0.979, 3.04, 1.156, 1.05]}, {"time": 1.3333, "value": 0.32, "curve": [1.379, 0.13, 1.423, 0]}, {"time": 1.4667, "curve": [1.689, 0, 1.911, 3.04]}, {"time": 2.1333, "value": 3.04, "curve": [2.312, 3.04, 2.489, 1.05]}, {"time": 2.6667, "value": 0.32, "curve": [2.713, 0.13, 2.756, 0]}, {"time": 2.8, "curve": [3.022, 0, 3.244, 3.04]}, {"time": 3.4667, "value": 3.04, "curve": [3.645, 3.04, 3.824, 1.09]}, {"time": 4, "value": 0.32}]}, "31": {"rotate": [{"value": 0.97, "curve": [0.068, 0.41, 0.134, 0]}, {"time": 0.2, "curve": [0.422, 0, 0.644, 4.5]}, {"time": 0.8667, "value": 4.5, "curve": [1.023, 4.5, 1.178, 2.24]}, {"time": 1.3333, "value": 0.97, "curve": [1.401, 0.41, 1.467, 0]}, {"time": 1.5333, "curve": [1.756, 0, 1.978, 4.5]}, {"time": 2.2, "value": 4.5, "curve": [2.356, 4.5, 2.511, 2.24]}, {"time": 2.6667, "value": 0.97, "curve": [2.735, 0.41, 2.801, 0]}, {"time": 2.8667, "curve": [3.089, 0, 3.311, 4.5]}, {"time": 3.5333, "value": 4.5, "curve": [3.69, 4.5, 3.846, 2.29]}, {"time": 4, "value": 0.97}]}, "26": {"rotate": [{"value": 2.21, "curve": [0.09, 1.02, 0.179, 0]}, {"time": 0.2667, "curve": [0.489, 0, 0.711, 6.29]}, {"time": 0.9333, "value": 6.29, "curve": [1.067, 6.29, 1.2, 3.98]}, {"time": 1.3333, "value": 2.21, "curve": [1.424, 1.02, 1.512, 0]}, {"time": 1.6, "curve": [1.822, 0, 2.044, 6.29]}, {"time": 2.2667, "value": 6.29, "curve": [2.401, 6.29, 2.533, 3.98]}, {"time": 2.6667, "value": 2.21, "curve": [2.757, 1.02, 2.845, 0]}, {"time": 2.9333, "curve": [3.156, 0, 3.378, 6.29]}, {"time": 3.6, "value": 6.29, "curve": [3.734, 6.29, 3.868, 4.03]}, {"time": 4, "value": 2.21}]}, "29": {"rotate": [{"value": -2.71, "curve": [0.113, -1.36, 0.223, 0]}, {"time": 0.3333, "curve": [0.556, 0, 0.778, -5.41]}, {"time": 1, "value": -5.41, "curve": [1.112, -5.41, 1.222, -4.03]}, {"time": 1.3333, "value": -2.71, "curve": [1.446, -1.36, 1.556, 0]}, {"time": 1.6667, "curve": [1.889, 0, 2.111, -5.41]}, {"time": 2.3333, "value": -5.41, "curve": [2.445, -5.41, 2.556, -4.03]}, {"time": 2.6667, "value": -2.71, "curve": [2.779, -1.36, 2.89, 0]}, {"time": 3, "curve": [3.222, 0, 3.444, -5.41]}, {"time": 3.6667, "value": -5.41, "curve": [3.779, -5.41, 3.89, -4.07]}, {"time": 4, "value": -2.71}]}, "30": {"rotate": [{"value": -3.51, "curve": [0.135, -1.95, 0.267, 0]}, {"time": 0.4, "curve": [0.622, 0, 0.844, -5.41]}, {"time": 1.0667, "value": -5.41, "curve": [1.156, -5.41, 1.244, -4.53]}, {"time": 1.3333, "value": -3.51, "curve": [1.468, -1.95, 1.601, 0]}, {"time": 1.7333, "curve": [1.956, 0, 2.178, -5.41]}, {"time": 2.4, "value": -5.41, "curve": [2.49, -5.41, 2.578, -4.53]}, {"time": 2.6667, "value": -3.51, "curve": [2.801, -1.95, 2.934, 0]}, {"time": 3.0667, "curve": [3.289, 0, 3.511, -5.41]}, {"time": 3.7333, "value": -5.41, "curve": [3.823, -5.41, 3.913, -4.56]}, {"time": 4, "value": -3.51}]}, "28": {"rotate": [{"value": -2.71, "curve": [0.113, -1.36, 0.223, 0]}, {"time": 0.3333, "curve": [0.556, 0, 0.778, -5.41]}, {"time": 1, "value": -5.41, "curve": [1.112, -5.41, 1.222, -4.03]}, {"time": 1.3333, "value": -2.71, "curve": [1.446, -1.36, 1.556, 0]}, {"time": 1.6667, "curve": [1.889, 0, 2.111, -5.41]}, {"time": 2.3333, "value": -5.41, "curve": [2.445, -5.41, 2.556, -4.03]}, {"time": 2.6667, "value": -2.71, "curve": [2.779, -1.36, 2.89, 0]}, {"time": 3, "curve": [3.222, 0, 3.444, -5.41]}, {"time": 3.6667, "value": -5.41, "curve": [3.779, -5.41, 3.89, -4.07]}, {"time": 4, "value": -2.71}]}, "32": {"rotate": [{"value": -3.51, "curve": [0.135, -1.95, 0.267, 0]}, {"time": 0.4, "curve": [0.622, 0, 0.844, -5.41]}, {"time": 1.0667, "value": -5.41, "curve": [1.156, -5.41, 1.244, -4.53]}, {"time": 1.3333, "value": -3.51, "curve": [1.468, -1.95, 1.601, 0]}, {"time": 1.7333, "curve": [1.956, 0, 2.178, -5.41]}, {"time": 2.4, "value": -5.41, "curve": [2.49, -5.41, 2.578, -4.53]}, {"time": 2.6667, "value": -3.51, "curve": [2.801, -1.95, 2.934, 0]}, {"time": 3.0667, "curve": [3.289, 0, 3.511, -5.41]}, {"time": 3.7333, "value": -5.41, "curve": [3.823, -5.41, 3.913, -4.56]}, {"time": 4, "value": -3.51}]}, "33": {"rotate": [{"value": -2.71, "curve": [0.113, -1.36, 0.223, 0]}, {"time": 0.3333, "curve": [0.556, 0, 0.778, -5.41]}, {"time": 1, "value": -5.41, "curve": [1.112, -5.41, 1.222, -4.03]}, {"time": 1.3333, "value": -2.71, "curve": [1.446, -1.36, 1.556, 0]}, {"time": 1.6667, "curve": [1.889, 0, 2.111, -5.41]}, {"time": 2.3333, "value": -5.41, "curve": [2.445, -5.41, 2.556, -4.03]}, {"time": 2.6667, "value": -2.71, "curve": [2.779, -1.36, 2.89, 0]}, {"time": 3, "curve": [3.222, 0, 3.444, -5.41]}, {"time": 3.6667, "value": -5.41, "curve": [3.779, -5.41, 3.89, -4.07]}, {"time": 4, "value": -2.71}]}, "34": {"rotate": [{"value": -3.51, "curve": [0.135, -1.95, 0.267, 0]}, {"time": 0.4, "curve": [0.622, 0, 0.844, -5.41]}, {"time": 1.0667, "value": -5.41, "curve": [1.156, -5.41, 1.244, -4.53]}, {"time": 1.3333, "value": -3.51, "curve": [1.468, -1.95, 1.601, 0]}, {"time": 1.7333, "curve": [1.956, 0, 2.178, -5.41]}, {"time": 2.4, "value": -5.41, "curve": [2.49, -5.41, 2.578, -4.53]}, {"time": 2.6667, "value": -3.51, "curve": [2.801, -1.95, 2.934, 0]}, {"time": 3.0667, "curve": [3.289, 0, 3.511, -5.41]}, {"time": 3.7333, "value": -5.41, "curve": [3.823, -5.41, 3.913, -4.56]}, {"time": 4, "value": -3.51}]}, "21": {"rotate": [{"value": -0.34, "curve": [0.046, -0.14, 0.09, 0]}, {"time": 0.1333, "curve": [0.356, 0, 0.578, -3.24]}, {"time": 0.8, "value": -3.24, "curve": [0.979, -3.24, 1.156, -1.11]}, {"time": 1.3333, "value": -0.34, "curve": [1.379, -0.14, 1.423, 0]}, {"time": 1.4667, "curve": [1.689, 0, 1.911, -3.24]}, {"time": 2.1333, "value": -3.24, "curve": [2.312, -3.24, 2.489, -1.11]}, {"time": 2.6667, "value": -0.34, "curve": [2.713, -0.14, 2.756, 0]}, {"time": 2.8, "curve": [3.022, 0, 3.244, -3.24]}, {"time": 3.4667, "value": -3.24, "curve": [3.645, -3.24, 3.824, -1.16]}, {"time": 4, "value": -0.34}]}, "25": {"rotate": [{"value": -1.45, "curve": [0.068, -0.62, 0.134, 0]}, {"time": 0.2, "curve": [0.422, 0, 0.644, -6.73]}, {"time": 0.8667, "value": -6.73, "curve": [1.023, -6.73, 1.178, -3.36]}, {"time": 1.3333, "value": -1.45, "curve": [1.401, -0.62, 1.467, 0]}, {"time": 1.5333, "curve": [1.756, 0, 1.978, -6.73]}, {"time": 2.2, "value": -6.73, "curve": [2.356, -6.73, 2.511, -3.36]}, {"time": 2.6667, "value": -1.45, "curve": [2.735, -0.62, 2.801, 0]}, {"time": 2.8667, "curve": [3.089, 0, 3.311, -6.73]}, {"time": 3.5333, "value": -6.73, "curve": [3.69, -6.73, 3.846, -3.43]}, {"time": 4, "value": -1.45}]}, "20": {"rotate": [{"value": 1.32, "curve": [0.09, 0.61, 0.179, 0]}, {"time": 0.2667, "curve": [0.489, 0, 0.711, 3.74]}, {"time": 0.9333, "value": 3.74, "curve": [1.067, 3.74, 1.2, 2.37]}, {"time": 1.3333, "value": 1.32, "curve": [1.424, 0.61, 1.512, 0]}, {"time": 1.6, "curve": [1.822, 0, 2.044, 3.74]}, {"time": 2.2667, "value": 3.74, "curve": [2.401, 3.74, 2.533, 2.37]}, {"time": 2.6667, "value": 1.32, "curve": [2.757, 0.61, 2.845, 0]}, {"time": 2.9333, "curve": [3.156, 0, 3.378, 3.74]}, {"time": 3.6, "value": 3.74, "curve": [3.734, 3.74, 3.868, 2.4]}, {"time": 4, "value": 1.32}]}, "23": {"rotate": [{"value": 1.56, "curve": [0.113, 0.78, 0.223, 0]}, {"time": 0.3333, "curve": [0.556, 0, 0.778, 3.11]}, {"time": 1, "value": 3.11, "curve": [1.112, 3.11, 1.222, 2.32]}, {"time": 1.3333, "value": 1.56, "curve": [1.446, 0.78, 1.556, 0]}, {"time": 1.6667, "curve": [1.889, 0, 2.111, 3.11]}, {"time": 2.3333, "value": 3.11, "curve": [2.445, 3.11, 2.556, 2.32]}, {"time": 2.6667, "value": 1.56, "curve": [2.779, 0.78, 2.89, 0]}, {"time": 3, "curve": [3.222, 0, 3.444, 3.11]}, {"time": 3.6667, "value": 3.11, "curve": [3.779, 3.11, 3.89, 2.34]}, {"time": 4, "value": 1.56}]}, "24": {"rotate": [{"value": 2.02, "curve": [0.135, 1.12, 0.267, 0]}, {"time": 0.4, "curve": [0.622, 0, 0.844, 3.11]}, {"time": 1.0667, "value": 3.11, "curve": [1.156, 3.11, 1.244, 2.61]}, {"time": 1.3333, "value": 2.02, "curve": [1.468, 1.12, 1.601, 0]}, {"time": 1.7333, "curve": [1.956, 0, 2.178, 3.11]}, {"time": 2.4, "value": 3.11, "curve": [2.49, 3.11, 2.578, 2.61]}, {"time": 2.6667, "value": 2.02, "curve": [2.801, 1.12, 2.934, 0]}, {"time": 3.0667, "curve": [3.289, 0, 3.511, 3.11]}, {"time": 3.7333, "value": 3.11, "curve": [3.823, 3.11, 3.913, 2.62]}, {"time": 4, "value": 2.02}]}, "22": {"rotate": [{"value": 1.56, "curve": [0.113, 0.78, 0.223, 0]}, {"time": 0.3333, "curve": [0.556, 0, 0.778, 3.11]}, {"time": 1, "value": 3.11, "curve": [1.112, 3.11, 1.222, 2.32]}, {"time": 1.3333, "value": 1.56, "curve": [1.446, 0.78, 1.556, 0]}, {"time": 1.6667, "curve": [1.889, 0, 2.111, 3.11]}, {"time": 2.3333, "value": 3.11, "curve": [2.445, 3.11, 2.556, 2.32]}, {"time": 2.6667, "value": 1.56, "curve": [2.779, 0.78, 2.89, 0]}, {"time": 3, "curve": [3.222, 0, 3.444, 3.11]}, {"time": 3.6667, "value": 3.11, "curve": [3.779, 3.11, 3.89, 2.34]}, {"time": 4, "value": 1.56}]}, "35": {"rotate": [{"value": 2.02, "curve": [0.135, 1.12, 0.267, 0]}, {"time": 0.4, "curve": [0.622, 0, 0.844, 3.11]}, {"time": 1.0667, "value": 3.11, "curve": [1.156, 3.11, 1.244, 2.61]}, {"time": 1.3333, "value": 2.02, "curve": [1.468, 1.12, 1.601, 0]}, {"time": 1.7333, "curve": [1.956, 0, 2.178, 3.11]}, {"time": 2.4, "value": 3.11, "curve": [2.49, 3.11, 2.578, 2.61]}, {"time": 2.6667, "value": 2.02, "curve": [2.801, 1.12, 2.934, 0]}, {"time": 3.0667, "curve": [3.289, 0, 3.511, 3.11]}, {"time": 3.7333, "value": 3.11, "curve": [3.823, 3.11, 3.913, 2.62]}, {"time": 4, "value": 2.02}]}, "36": {"rotate": [{"value": 1.56, "curve": [0.113, 0.78, 0.223, 0]}, {"time": 0.3333, "curve": [0.556, 0, 0.778, 3.11]}, {"time": 1, "value": 3.11, "curve": [1.112, 3.11, 1.222, 2.32]}, {"time": 1.3333, "value": 1.56, "curve": [1.446, 0.78, 1.556, 0]}, {"time": 1.6667, "curve": [1.889, 0, 2.111, 3.11]}, {"time": 2.3333, "value": 3.11, "curve": [2.445, 3.11, 2.556, 2.32]}, {"time": 2.6667, "value": 1.56, "curve": [2.779, 0.78, 2.89, 0]}, {"time": 3, "curve": [3.222, 0, 3.444, 3.11]}, {"time": 3.6667, "value": 3.11, "curve": [3.779, 3.11, 3.89, 2.34]}, {"time": 4, "value": 1.56}]}, "37": {"rotate": [{"value": 2.02, "curve": [0.135, 1.12, 0.267, 0]}, {"time": 0.4, "curve": [0.622, 0, 0.844, 3.11]}, {"time": 1.0667, "value": 3.11, "curve": [1.156, 3.11, 1.244, 2.61]}, {"time": 1.3333, "value": 2.02, "curve": [1.468, 1.12, 1.601, 0]}, {"time": 1.7333, "curve": [1.956, 0, 2.178, 3.11]}, {"time": 2.4, "value": 3.11, "curve": [2.49, 3.11, 2.578, 2.61]}, {"time": 2.6667, "value": 2.02, "curve": [2.801, 1.12, 2.934, 0]}, {"time": 3.0667, "curve": [3.289, 0, 3.511, 3.11]}, {"time": 3.7333, "value": 3.11, "curve": [3.823, 3.11, 3.913, 2.62]}, {"time": 4, "value": 2.02}]}, "43": {"rotate": [{"value": -9.85, "curve": [0.046, -10.59, 0.09, -11.1]}, {"time": 0.1333, "value": -11.1, "curve": [0.356, -11.1, 0.578, 0.94]}, {"time": 0.8, "value": 0.94, "curve": [0.979, 0.94, 1.156, -6.95]}, {"time": 1.3333, "value": -9.85, "curve": [1.379, -10.59, 1.423, -11.1]}, {"time": 1.4667, "value": -11.1, "curve": [1.689, -11.1, 1.911, 0.94]}, {"time": 2.1333, "value": 0.94, "curve": [2.312, 0.94, 2.489, -6.95]}, {"time": 2.6667, "value": -9.85, "curve": [2.713, -10.59, 2.756, -11.1]}, {"time": 2.8, "value": -11.1, "curve": [3.022, -11.1, 3.244, 0.94]}, {"time": 3.4667, "value": 0.94, "curve": [3.645, 0.94, 3.824, -6.79]}, {"time": 4, "value": -9.85}]}, "44": {"rotate": [{"value": -4.08, "curve": [0.09, -7.87, 0.179, -11.1]}, {"time": 0.2667, "value": -11.1, "curve": [0.489, -11.1, 0.711, 8.83]}, {"time": 0.9333, "value": 8.83, "curve": [1.067, 8.83, 1.2, 1.5]}, {"time": 1.3333, "value": -4.08, "curve": [1.424, -7.87, 1.512, -11.1]}, {"time": 1.6, "value": -11.1, "curve": [1.822, -11.1, 2.044, 8.83]}, {"time": 2.2667, "value": 8.83, "curve": [2.401, 8.83, 2.533, 1.5]}, {"time": 2.6667, "value": -4.08, "curve": [2.757, -7.87, 2.845, -11.1]}, {"time": 2.9333, "value": -11.1, "curve": [3.156, -11.1, 3.378, 8.83]}, {"time": 3.6, "value": 8.83, "curve": [3.734, 8.83, 3.868, 1.67]}, {"time": 4, "value": -4.08}]}, "45": {"rotate": [{"value": 3.41, "curve": [0.135, -3.02, 0.267, -11.1]}, {"time": 0.4, "value": -11.1, "curve": [0.622, -11.1, 0.844, 11.3]}, {"time": 1.0667, "value": 11.3, "curve": [1.156, 11.3, 1.244, 7.66]}, {"time": 1.3333, "value": 3.41, "curve": [1.468, -3.02, 1.601, -11.1]}, {"time": 1.7333, "value": -11.1, "curve": [1.956, -11.1, 2.178, 11.3]}, {"time": 2.4, "value": 11.3, "curve": [2.49, 11.3, 2.578, 7.66]}, {"time": 2.6667, "value": 3.41, "curve": [2.801, -3.02, 2.934, -11.1]}, {"time": 3.0667, "value": -11.1, "curve": [3.289, -11.1, 3.511, 11.3]}, {"time": 3.7333, "value": 11.3, "curve": [3.823, 11.3, 3.913, 7.76]}, {"time": 4, "value": 3.41}]}, "46": {"rotate": [{"value": 19.08, "curve": [0.179, 10.38, 0.356, -11.1]}, {"time": 0.5333, "value": -11.1, "curve": [0.756, -11.1, 0.978, 22.58]}, {"time": 1.2, "value": 22.58, "curve": [1.245, 22.58, 1.289, 21.23]}, {"time": 1.3333, "value": 19.08, "curve": [1.513, 10.38, 1.69, -11.1]}, {"time": 1.8667, "value": -11.1, "curve": [2.089, -11.1, 2.311, 22.58]}, {"time": 2.5333, "value": 22.58, "curve": [2.579, 22.58, 2.622, 21.23]}, {"time": 2.6667, "value": 19.08, "curve": [2.846, 10.38, 3.023, -11.1]}, {"time": 3.2, "value": -11.1, "curve": [3.422, -11.1, 3.644, 22.58]}, {"time": 3.8667, "value": 22.58, "curve": [3.912, 22.58, 3.957, 21.29]}, {"time": 4, "value": 19.08}]}, "17": {"rotate": [{"value": 4.3, "curve": [0.068, 5.8, 0.134, 6.91]}, {"time": 0.2, "value": 6.91, "curve": [0.422, 6.91, 0.644, -5.18]}, {"time": 0.8667, "value": -5.18, "curve": [1.023, -5.18, 1.178, 0.88]}, {"time": 1.3333, "value": 4.3, "curve": [1.401, 5.8, 1.467, 6.91]}, {"time": 1.5333, "value": 6.91, "curve": [1.756, 6.91, 1.978, -5.18]}, {"time": 2.2, "value": -5.18, "curve": [2.356, -5.18, 2.511, 0.88]}, {"time": 2.6667, "value": 4.3, "curve": [2.735, 5.8, 2.801, 6.91]}, {"time": 2.8667, "value": 6.91, "curve": [3.089, 6.91, 3.311, -5.18]}, {"time": 3.5333, "value": -5.18, "curve": [3.69, -5.18, 3.846, 0.75]}, {"time": 4, "value": 4.3}]}, "18": {"rotate": [{"value": -0.03, "curve": [0.124, 3.25, 0.245, 6.91]}, {"time": 0.3667, "value": 6.91, "curve": [0.589, 6.91, 0.811, -5.18]}, {"time": 1.0333, "value": -5.18, "curve": [1.134, -5.18, 1.233, -2.68]}, {"time": 1.3333, "value": -0.03, "curve": [1.457, 3.25, 1.579, 6.91]}, {"time": 1.7, "value": 6.91, "curve": [1.922, 6.91, 2.144, -5.18]}, {"time": 2.3667, "value": -5.18, "curve": [2.467, -5.18, 2.567, -2.68]}, {"time": 2.6667, "value": -0.03, "curve": [2.79, 3.25, 2.912, 6.91]}, {"time": 3.0333, "value": 6.91, "curve": [3.256, 6.91, 3.478, -5.18]}, {"time": 3.7, "value": -5.18, "curve": [3.801, -5.18, 3.901, -2.75]}, {"time": 4, "value": -0.03}]}, "7": {"rotate": [{"value": 4.37, "curve": [0.09, 2.01, 0.179, 0]}, {"time": 0.2667, "curve": [0.489, 0, 0.711, 12.41]}, {"time": 0.9333, "value": 12.41, "curve": [1.067, 12.41, 1.2, 7.85]}, {"time": 1.3333, "value": 4.37, "curve": [1.424, 2.01, 1.512, 0]}, {"time": 1.6, "curve": [1.822, 0, 2.044, 12.41]}, {"time": 2.2667, "value": 12.41, "curve": [2.401, 12.41, 2.533, 7.85]}, {"time": 2.6667, "value": 4.37, "curve": [2.757, 2.01, 2.845, 0]}, {"time": 2.9333, "curve": [3.156, 0, 3.378, 12.41]}, {"time": 3.6, "value": 12.41, "curve": [3.734, 12.41, 3.868, 7.95]}, {"time": 4, "value": 4.37}]}, "9": {"rotate": [{"value": -3.38, "curve": [0.135, -1.88, 0.267, 0]}, {"time": 0.4, "curve": [0.622, 0, 0.844, -5.22]}, {"time": 1.0667, "value": -5.22, "curve": [1.156, -5.22, 1.244, -4.37]}, {"time": 1.3333, "value": -3.38, "curve": [1.468, -1.88, 1.601, 0]}, {"time": 1.7333, "curve": [1.956, 0, 2.178, -5.22]}, {"time": 2.4, "value": -5.22, "curve": [2.49, -5.22, 2.578, -4.37]}, {"time": 2.6667, "value": -3.38, "curve": [2.801, -1.88, 2.934, 0]}, {"time": 3.0667, "curve": [3.289, 0, 3.511, -5.22]}, {"time": 3.7333, "value": -5.22, "curve": [3.823, -5.22, 3.913, -4.39]}, {"time": 4, "value": -3.38}]}, "10": {"rotate": [{"value": -4.68, "curve": [0.179, -3.33, 0.356, 0]}, {"time": 0.5333, "curve": [0.756, 0, 0.978, -5.22]}, {"time": 1.2, "value": -5.22, "curve": [1.245, -5.22, 1.289, -5.01]}, {"time": 1.3333, "value": -4.68, "curve": [1.513, -3.33, 1.69, 0]}, {"time": 1.8667, "curve": [2.089, 0, 2.311, -5.22]}, {"time": 2.5333, "value": -5.22, "curve": [2.579, -5.22, 2.622, -5.01]}, {"time": 2.6667, "value": -4.68, "curve": [2.846, -3.33, 3.023, 0]}, {"time": 3.2, "curve": [3.422, 0, 3.644, -5.22]}, {"time": 3.8667, "value": -5.22, "curve": [3.912, -5.22, 3.957, -5.02]}, {"time": 4, "value": -4.68}]}, "13": {"rotate": [{"value": -1.69, "curve": [0.09, -0.78, 0.179, 0]}, {"time": 0.2667, "curve": [0.489, 0, 0.711, -4.81]}, {"time": 0.9333, "value": -4.81, "curve": [1.067, -4.81, 1.2, -3.04]}, {"time": 1.3333, "value": -1.69, "curve": [1.424, -0.78, 1.512, 0]}, {"time": 1.6, "curve": [1.822, 0, 2.044, -4.81]}, {"time": 2.2667, "value": -4.81, "curve": [2.401, -4.81, 2.533, -3.04]}, {"time": 2.6667, "value": -1.69, "curve": [2.757, -0.78, 2.845, 0]}, {"time": 2.9333, "curve": [3.156, 0, 3.378, -4.81]}, {"time": 3.6, "value": -4.81, "curve": [3.734, -4.81, 3.868, -3.08]}, {"time": 4, "value": -1.69}]}, "14": {"rotate": [{"value": -6.17, "curve": [0.135, -3.43, 0.267, 0]}, {"time": 0.4, "curve": [0.622, 0, 0.844, -9.52]}, {"time": 1.0667, "value": -9.52, "curve": [1.156, -9.52, 1.244, -7.97]}, {"time": 1.3333, "value": -6.17, "curve": [1.468, -3.43, 1.601, 0]}, {"time": 1.7333, "curve": [1.956, 0, 2.178, -9.52]}, {"time": 2.4, "value": -9.52, "curve": [2.49, -9.52, 2.578, -7.97]}, {"time": 2.6667, "value": -6.17, "curve": [2.801, -3.43, 2.934, 0]}, {"time": 3.0667, "curve": [3.289, 0, 3.511, -9.52]}, {"time": 3.7333, "value": -9.52, "curve": [3.823, -9.52, 3.913, -8.01]}, {"time": 4, "value": -6.17}]}, "15": {"rotate": [{"value": -8.53, "curve": [0.179, -6.07, 0.356, 0]}, {"time": 0.5333, "curve": [0.756, 0, 0.978, -9.52]}, {"time": 1.2, "value": -9.52, "curve": [1.245, -9.52, 1.289, -9.14]}, {"time": 1.3333, "value": -8.53, "curve": [1.513, -6.07, 1.69, 0]}, {"time": 1.8667, "curve": [2.089, 0, 2.311, -9.52]}, {"time": 2.5333, "value": -9.52, "curve": [2.579, -9.52, 2.622, -9.14]}, {"time": 2.6667, "value": -8.53, "curve": [2.846, -6.07, 3.023, 0]}, {"time": 3.2, "curve": [3.422, 0, 3.644, -9.52]}, {"time": 3.8667, "value": -9.52, "curve": [3.912, -9.52, 3.957, -9.15]}, {"time": 4, "value": -8.53}]}, "2": {"translate": [{"curve": [0.222, 0, 0.444, 0.16, 0.222, 0, 0.444, 10.54]}, {"time": 0.6667, "x": 0.16, "y": 10.54, "curve": [0.889, 0.16, 1.111, 0, 0.889, 10.54, 1.111, 0]}, {"time": 1.3333, "curve": [1.556, 0, 1.778, 0.16, 1.556, 0, 1.778, 10.54]}, {"time": 2, "x": 0.16, "y": 10.54, "curve": [2.222, 0.16, 2.444, 0, 2.222, 10.54, 2.444, 0]}, {"time": 2.6667, "curve": [2.889, 0, 3.111, 0.16, 2.889, 0, 3.111, 10.54]}, {"time": 3.3333, "x": 0.16, "y": 10.54, "curve": [3.556, 0.16, 3.778, 0, 3.556, 10.54, 3.778, 0]}, {"time": 4}]}, "1": {"scale": [{"curve": [0.222, 1, 0.444, 1.028, 0.222, 1, 0.444, 1.028]}, {"time": 0.6667, "x": 1.028, "y": 1.028, "curve": [0.889, 1.028, 1.111, 1, 0.889, 1.028, 1.111, 1]}, {"time": 1.3333, "curve": [1.556, 1, 1.778, 1.028, 1.556, 1, 1.778, 1.028]}, {"time": 2, "x": 1.028, "y": 1.028, "curve": [2.222, 1.028, 2.444, 1, 2.222, 1.028, 2.444, 1]}, {"time": 2.6667, "curve": [2.889, 1, 3.111, 1.028, 2.889, 1, 3.111, 1.028]}, {"time": 3.3333, "x": 1.028, "y": 1.028, "curve": [3.556, 1.028, 3.778, 1, 3.556, 1.028, 3.778, 1]}, {"time": 4}]}, "6": {"translate": [{"x": 1.63, "y": -0.04, "curve": [0.09, 0.75, 0.179, 0, 0.09, -0.02, 0.179, 0]}, {"time": 0.2667, "curve": [0.489, 0, 0.711, 4.64, 0.489, 0, 0.711, -0.11]}, {"time": 0.9333, "x": 4.64, "y": -0.11, "curve": [1.067, 4.64, 1.2, 2.94, 1.067, -0.11, 1.2, -0.07]}, {"time": 1.3333, "x": 1.63, "y": -0.04, "curve": [1.424, 0.75, 1.512, 0, 1.424, -0.02, 1.512, 0]}, {"time": 1.6, "curve": [1.822, 0, 2.044, 4.64, 1.822, 0, 2.044, -0.11]}, {"time": 2.2667, "x": 4.64, "y": -0.11, "curve": [2.401, 4.64, 2.533, 2.94, 2.401, -0.11, 2.533, -0.07]}, {"time": 2.6667, "x": 1.63, "y": -0.04, "curve": [2.757, 0.75, 2.845, 0, 2.757, -0.02, 2.845, 0]}, {"time": 2.9333, "curve": [3.156, 0, 3.378, 4.64, 3.156, 0, 3.378, -0.11]}, {"time": 3.6, "x": 4.64, "y": -0.11, "curve": [3.734, 4.64, 3.868, 2.98, 3.734, -0.11, 3.868, -0.07]}, {"time": 4, "x": 1.63, "y": -0.04}]}, "5": {"translate": [{"x": 1.63, "y": -0.04, "curve": [0.09, 0.75, 0.179, 0, 0.09, -0.02, 0.179, 0]}, {"time": 0.2667, "curve": [0.489, 0, 0.711, 4.64, 0.489, 0, 0.711, -0.11]}, {"time": 0.9333, "x": 4.64, "y": -0.11, "curve": [1.067, 4.64, 1.2, 2.94, 1.067, -0.11, 1.2, -0.07]}, {"time": 1.3333, "x": 1.63, "y": -0.04, "curve": [1.424, 0.75, 1.512, 0, 1.424, -0.02, 1.512, 0]}, {"time": 1.6, "curve": [1.822, 0, 2.044, 4.64, 1.822, 0, 2.044, -0.11]}, {"time": 2.2667, "x": 4.64, "y": -0.11, "curve": [2.401, 4.64, 2.533, 2.94, 2.401, -0.11, 2.533, -0.07]}, {"time": 2.6667, "x": 1.63, "y": -0.04, "curve": [2.757, 0.75, 2.845, 0, 2.757, -0.02, 2.845, 0]}, {"time": 2.9333, "curve": [3.156, 0, 3.378, 4.64, 3.156, 0, 3.378, -0.11]}, {"time": 3.6, "x": 4.64, "y": -0.11, "curve": [3.734, 4.64, 3.868, 2.98, 3.734, -0.11, 3.868, -0.07]}, {"time": 4, "x": 1.63, "y": -0.04}]}, "4": {"scale": [{"time": 1.3333, "curve": [1.367, 0.687, 1.4, 0.06, 1.367, 1, 1.4, 1]}, {"time": 1.4333, "x": 0.06, "curve": [1.467, 0.06, 1.5, 1, 1.467, 1, 1.5, 1]}, {"time": 1.5333}]}, "3": {"scale": [{"time": 1.3333, "curve": [1.367, 0.687, 1.4, 0.06, 1.367, 1, 1.4, 1]}, {"time": 1.4333, "x": 0.06, "curve": [1.467, 0.06, 1.5, 1, 1.467, 1, 1.5, 1]}, {"time": 1.5333}]}}}}}