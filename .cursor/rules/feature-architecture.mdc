---
description: 
globs: 
alwaysApply: false
---
# Feature-Based Architecture

## Feature Structure
Each feature should be organized in a modular way:
```
features/
└── featureName/
    ├── components/     # Vue components specific to this feature
    ├── composables/    # Feature-specific composable functions
    ├── stores/         # Feature-specific Pinia stores
    ├── api/           # API endpoints for this feature
    ├── types/         # TypeScript types/interfaces
    └── index.js       # Public API exports
```

## Store Guidelines
- Keep stores focused on specific feature state
- Use Pinia's setup syntax for cleaner stores:
  ```js
  // Example store pattern
  export const useFeatureStore = defineStore('featureName', () => {
    // State
    const isVisible = ref(false)
    const data = ref(null)

    // Actions
    function showFeature(payload) {
      data.value = payload
      isVisible.value = true
    }

    function hideFeature() {
      isVisible.value = false
      data.value = null
    }

    return {
      isVisible,
      data,
      showFeature,
      hideFeature
    }
  })
  ```
- Export stores through feature's index.js
- Use stores for:
  - UI state (modals, popups, overlays)
  - Feature-specific data caching
  - Complex state management
  - Cross-component communication

## Composables Guidelines
- Keep business logic in composables, not components
- Use Vue's Composition API with TypeScript
- Follow a clear pattern for state management:
  ```js
  // Example pattern
  const someData = ref(null)
  export const useSomeFeature = () => {
    const fetchData = async () => {
      // API calls and data manipulation
    }
    return {
      someData,
      fetchData
    }
  }
  ```
- Handle errors consistently using notification system
- Use ref() for reactive state
- Export composables through feature's index.js

## API Organization
- Group API calls by feature
- Export API functions through feature's index.js
- Use axios instance with interceptors for consistent error handling
- Follow RESTful principles
- Handle authentication/errors at the HTTP client level

## Component Guidelines
- Keep components small and focused
- Minimize logic in components
- Use composables for complex logic and state
- Follow proper prop typing with TypeScript interfaces
- Use slots for flexible content injection
- Implement proper error boundaries

## Feature Integration
- Export all public APIs through index.js
- Features can import from other features via their public API
- Avoid circular dependencies between features
- Share common utilities through global utils
- Use proper TypeScript types for all exports

## State Management Hierarchy
1. Component State:
   - Use refs for local component state
   - Keep UI-specific state in components

2. Feature State:
   - Use composables for feature-specific logic
   - Use stores for feature-wide state
   - Handle data caching in stores

3. Global State:
   - Use global Pinia stores for app-wide state
   - Keep global state minimal

## Code Style
- Use TypeScript for all new code
- Write clean, readable code with proper naming
- Keep functions small and focused
- Use proper error handling
- Document complex logic
- Use proper typing for all variables and functions

## Feature Communication
- Use events for component communication
- Use composables for sharing state between components
- Use stores for complex state management
- Implement proper loading and error states
- Use TypeScript for type-safe communication

## Testing
- Write unit tests for composables and stores
- Test components in isolation
- Mock API calls appropriately
- Use proper TypeScript types in tests



