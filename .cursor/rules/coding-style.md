# Coding Style Standards

Follow these coding standards and patterns:

1. Code Style and Structure
- Write concise, technical code with accurate examples
- Use functional and declarative programming patterns; avoid classes
- Prefer iteration and modularization over code duplication
- Use descriptive variable names with auxiliary verbs (e.g., isLoading, hasError)
- Structure files: exported component, subcomponents, helpers, static content, types
- Avoid unnecessary comments in component styles; CSS properties should be self-descriptive

2. Naming Conventions
- Use lowercase with dashes for directories (e.g., components/auth-wizard)
- <PERSON>avor named exports for components

3. TypeScript Usage
- Use TypeScript for all code; prefer interfaces over types
- Avoid enums; use maps instead
- Use functional components with TypeScript interfaces

4. Syntax and Formatting
- Use the "function" keyword for pure functions
- Avoid unnecessary curly braces in conditionals; use concise syntax for simple statements
- Use declarative code style

5. Performance Optimization
- Minimize side effects and state mutations
- Use proper lazy loading and code splitting where appropriate
- Optimize for performance without premature optimization 