---
description: 
globs: 
alwaysApply: true
---
# Project Structure and API Reference

## Project Organization
- Follow a clear separation of concerns
- Group related functionality into feature modules
- Keep shared utilities and components in dedicated directories
- Use proper layering (UI components, business logic, data access)

## API Integration
- Reference moofun API documentation for endpoints and data structures
- Follow RESTful principles for API interactions
- Implement proper error handling for API calls
- Use type-safe API client implementations

## Directory Structure
```
moofun/
├── src/
│   ├── components/     # Reusable UI components
│   ├── features/       # Feature-specific modules
│   ├── api/           # API client and types
│   ├── utils/         # Shared utilities
│   └── styles/        # Global styles
└── public/            # Static assets
```

## API Guidelines
- Use proper authentication headers
- Handle API errors gracefully
- Implement request caching where appropriate
- Follow API versioning conventions

