# API Configuration
VITE_API_BASE_URL=https://wolf.jpegonapechain.com/api

# Telegram Configuration
# 开发环境的Telegram初始化数据后备值
VITE_TELEGRAM_INIT_DATA_FALLBACK="user=%7B%22id%22%3A1234567890%2C%22first_name%22%3A%22Test%22%2C%22last_name%22%3A%22User%22%2C%22language_code%22%3A%22en%22%7D&chat_instance=1234567890&chat_type=sender&auth_date=1234567890&hash=abcdef1234567890"

# DApp Portal SDK Configuration
# 从 @linenext/dapp-portal-sdk 获取的客户端ID
VITE_DAPP_CLIENT_ID=your_dapp_client_id_here

# 环境变量说明：
# 
# VITE_API_BASE_URL: 
#   - 后端API的基础URL
#   - 用于所有API请求的baseURL
#
# VITE_TELEGRAM_INIT_DATA_FALLBACK:
#   - 当Telegram WebApp SDK不可用时的后备数据
#   - 主要用于开发环境测试
#   - 生产环境通常不需要设置
#
# VITE_DAPP_CLIENT_ID:
#   - DApp Portal SDK的客户端标识符
#   - 用于Kaia区块链集成
#   - 需要从@linenext/dapp-portal-sdk提供商获取
#
# 注意：
# - 所有以VITE_开头的变量都会在构建时被注入到客户端代码中
# - 不要在这些变量中存储敏感信息，因为它们会暴露给客户端
# - DEV变量由Vite自动管理，无需手动设置
