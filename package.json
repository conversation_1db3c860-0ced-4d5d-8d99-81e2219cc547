{"name": "moofun-project", "version": "1.1.1", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .eslint<PERSON>ore", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,vue,css,scss}\"", "lint:check": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --ignore-path .eslintignore", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,vue,css,scss}\""}, "dependencies": {"@esotericsoftware/spine-phaser": "^4.2.38", "@fontsource/rammetto-one": "^5.1.1", "@linenext/dapp-portal-sdk": "^1.2.12", "@tonconnect/ui": "^2.0.11", "axios": "^1.9.0", "phaser": "^3.87.0", "pinia": "^2.3.1", "pixi.js": "^8.9.2", "vue": "^3.4.15", "vue-i18n": "^10.0.7", "vue-router": "^4.5.1"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-vue": "^10.1.0", "prettier": "^3.5.3", "vite": "^6.3.5"}, "main": "vite.config.js", "keywords": [], "author": "", "license": "ISC", "description": ""}