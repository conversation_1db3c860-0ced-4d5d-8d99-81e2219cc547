// 简单的API测试脚本
const axios = require('axios');

// 模拟环境变量
process.env.VITE_API_BASE_URL = 'https://wolf.jpegonapechain.com/api';

// 创建API客户端
const apiClient = axios.create({
    baseURL: process.env.VITE_API_BASE_URL,
    headers: { 'Content-Type': 'application/json' }
});

// 测试API连接
async function testAPI() {
    console.log('测试API配置...');
    console.log('Base URL:', process.env.VITE_API_BASE_URL);
    
    try {
        // 测试基础连接
        const response = await apiClient.get('/user/me', {
            validateStatus: function (status) {
                // 接受401状态码，因为我们没有认证token
                return status < 500;
            }
        });
        
        console.log('✅ API连接成功');
        console.log('状态码:', response.status);
        console.log('响应头:', response.headers);
        
        if (response.status === 401) {
            console.log('✅ 401状态码正常 - 需要认证token');
        }
        
    } catch (error) {
        console.error('❌ API连接失败:', error.message);
        
        if (error.code === 'ENOTFOUND') {
            console.error('❌ DNS解析失败 - 检查网络连接');
        } else if (error.code === 'ECONNREFUSED') {
            console.error('❌ 连接被拒绝 - 服务器可能不可用');
        }
    }
}

testAPI();
